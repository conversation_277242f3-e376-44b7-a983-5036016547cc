# 快速开始指南

## 🚀 5分钟快速启动

### 第一步：检查环境
```bash
# 确保Python版本
python --version  # 需要3.8+

# 进入项目目录
cd "新闻分析-最简版本"
```

### 第二步：安装依赖
```bash
pip install -r requirements.txt
```

### 第三步：配置API密钥
编辑 `config.json` 文件，填入您的API密钥：

```json
{
  "analyzer": {
    "gemini": {
      "api_key": "您的Gemini API密钥"
    },
    "deepseek": {
      "api_key": "您的DeepSeek API密钥"
    }
  },
  "pusher": {
    "username": "您的邮箱@qq.com",
    "password": "邮箱应用密码",
    "to_email": "接收邮箱@qq.com"
  }
}
```

### 第四步：启动系统

#### 🖱️ Windows用户（推荐）
```bash
# 方式1：快速启动（最简单）
双击 "快速启动.bat"

# 方式2：完整选项菜单
双击 "start_persistent.bat"

# 方式3：后台运行（无窗口）
双击 "后台运行.bat"
# 停止后台运行：双击 "停止后台运行.bat"
```

#### 💻 命令行用户
```bash
# 测试运行（单次）
python main.py

# 持久运行（推荐）
python main_persistent.py

# 使用启动脚本
python start_persistent.py --mode run
```

## 🔑 API密钥获取

### Gemini API密钥
1. 访问：https://aistudio.google.com/
2. 登录Google账号
3. 创建API密钥
4. 复制密钥到配置文件

### DeepSeek API密钥
1. 访问：https://platform.deepseek.com/
2. 注册并登录
3. 在控制台创建API密钥
4. 复制密钥到配置文件

## 📧 邮箱配置

### QQ邮箱设置
1. 登录QQ邮箱
2. 设置 → 账户
3. 开启SMTP服务
4. 获取**应用专用密码**（不是登录密码！）
5. 将应用密码填入配置文件

### 其他邮箱
- **163邮箱**：smtp.163.com，端口587
- **Gmail**：smtp.gmail.com，端口587
- **企业邮箱**：咨询管理员获取SMTP设置

## ⚙️ 常用配置

### 调整运行频率
```json
"scheduler": {
  "interval_minutes": 15  // 改为15分钟运行一次
}
```

### 禁用夜间运行
```json
"scheduler": {
  "quiet_hours": {
    "enabled": true,
    "start_hour": 22,  // 22点开始静默
    "end_hour": 8      // 8点结束静默
  }
}
```

### 切换主要AI模型
```json
"analyzer": {
  "primary_provider": "deepseek",  // 使用DeepSeek作为主要模型
  "fallback_provider": "gemini"    // Gemini作为备用
}
```

## 🔍 常见问题

### Q: 收到"分析模型: mock"邮件？
A: 说明API调用失败，检查：
- API密钥是否正确
- 网络连接是否正常
- API配额是否充足

### Q: 邮件发送失败？
A: 检查：
- 使用应用专用密码，不是登录密码
- SMTP设置是否正确
- 邮箱是否开启SMTP服务

### Q: 无法获取新闻？
A: 检查：
- 网络连接是否正常
- 财联社网站是否可访问
- 是否被网站限制访问

### Q: 如何停止系统？
A: 在运行窗口按 `Ctrl+C` 优雅停止

## 📝 日志查看

系统运行时会生成日志文件：
```bash
# 查看运行日志
tail -f news_analysis.log

# Windows用户可以直接打开文件查看
```

## 🎯 成功标志

系统正常运行时，您会看到：
1. ✅ 控制台显示"获取到X条新闻"
2. ✅ 控制台显示"完成X条新闻分析"
3. ✅ 控制台显示"结果推送完成"
4. ✅ 邮箱收到分析报告（使用真实AI模型）

## 🛑 如何停止

### 临时停止
在运行窗口按 `Ctrl+C`

### 永久停止
删除或重命名 `start_persistent.bat` 文件

## 📞 技术支持

如遇问题：
1. 查看 `news_analysis.log` 日志文件
2. 检查配置文件格式是否正确
3. 确认API密钥和邮箱配置
4. 测试网络连接

---

🎉 **恭喜！您的新闻分析系统已经配置完成！**

现在系统会自动：
- 每30分钟获取最新财经新闻
- 使用AI进行专业投研分析
- 将分析结果发送到您的邮箱
- 夜间自动暂停，白天自动恢复
