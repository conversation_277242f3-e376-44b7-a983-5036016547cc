#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模型优先级测试脚本

功能：测试新的模型优先级配置和切换机制
"""

import asyncio
import logging
from config import load_config, setup_logging
from analyzer import NewsAnalyzer

# 测试新闻数据
TEST_NEWS = {
    "title": "测试新闻：某科技公司发布新产品",
    "content": "某知名科技公司今日发布了一款革命性的新产品，预计将对行业产生重大影响。该产品采用了最新的技术，具有多项创新功能，市场反应积极。",
    "url": "https://test.com/news1",
    "publish_time": "2024-01-01 10:00:00"
}

async def test_model_configuration():
    """测试模型配置"""
    print("🧪 测试模型配置...")
    
    config = load_config()
    analyzer_config = config.get('analyzer', {})
    
    primary_provider = analyzer_config.get('primary_provider')
    fallback_provider = analyzer_config.get('fallback_provider')
    
    print(f"🎯 主要模型: {primary_provider}")
    print(f"🔄 备用模型: {fallback_provider}")
    
    # 验证配置
    expected_primary = "gemini"
    expected_fallback = "kimi"
    
    if primary_provider == expected_primary and fallback_provider == expected_fallback:
        print("✅ 模型优先级配置正确")
        return True
    else:
        print(f"❌ 模型优先级配置错误")
        print(f"   期望: 主要={expected_primary}, 备用={expected_fallback}")
        print(f"   实际: 主要={primary_provider}, 备用={fallback_provider}")
        return False

async def test_primary_model():
    """测试主要模型（Gemini）"""
    print("\n🧪 测试主要模型 (Gemini)...")
    
    config = load_config()
    logger = setup_logging(config)
    analyzer = NewsAnalyzer(config)
    
    try:
        result = await analyzer.analyze_news(TEST_NEWS)
        model_used = result.get('model_used', 'unknown')
        
        if 'gemini' in model_used.lower():
            print(f"✅ 主要模型测试成功，使用模型: {model_used}")
            print(f"📊 分析结果长度: {len(result.get('markdown_report', ''))}")
            return True
        else:
            print(f"⚠️ 主要模型未使用，实际使用: {model_used}")
            return False
            
    except Exception as e:
        print(f"❌ 主要模型测试失败: {e}")
        return False

async def test_fallback_mechanism():
    """测试备用模型切换机制"""
    print("\n🧪 测试备用模型切换机制...")
    
    config = load_config()
    
    # 临时禁用 Gemini 来测试切换机制
    original_gemini_key = config['analyzer']['gemini']['api_key']
    config['analyzer']['gemini']['api_key'] = 'invalid_key_for_testing'
    
    logger = setup_logging(config)
    analyzer = NewsAnalyzer(config)
    
    try:
        result = await analyzer.analyze_news(TEST_NEWS)
        model_used = result.get('model_used', 'unknown')
        
        if 'kimi' in model_used.lower() and 'fallback' in model_used.lower():
            print(f"✅ 备用模型切换成功，使用模型: {model_used}")
            print(f"📊 分析结果长度: {len(result.get('markdown_report', ''))}")
            return True
        else:
            print(f"❌ 备用模型切换失败，实际使用: {model_used}")
            return False
            
    except Exception as e:
        print(f"❌ 备用模型测试失败: {e}")
        return False
    finally:
        # 恢复原始配置
        config['analyzer']['gemini']['api_key'] = original_gemini_key

async def test_all_models_available():
    """测试所有模型配置是否可用"""
    print("\n🧪 测试所有模型配置...")
    
    config = load_config()
    analyzer_config = config.get('analyzer', {})
    
    models = ['gemini', 'kimi', 'deepseek']
    results = {}
    
    for model in models:
        model_config = analyzer_config.get(model, {})
        api_key = model_config.get('api_key', '')
        enabled = model_config.get('enabled', False)
        
        if api_key and enabled:
            results[model] = "✅ 已配置"
        elif enabled and not api_key:
            results[model] = "⚠️ 已启用但缺少API密钥"
        elif api_key and not enabled:
            results[model] = "⚠️ 有API密钥但未启用"
        else:
            results[model] = "❌ 未配置"
    
    print("📋 模型配置状态:")
    for model, status in results.items():
        print(f"  {model}: {status}")
    
    # 检查是否所有模型都可用
    all_available = all("✅" in status for status in results.values())
    
    if all_available:
        print("✅ 所有模型都已正确配置")
    else:
        print("⚠️ 部分模型配置不完整")
    
    return all_available

async def test_batch_processing():
    """测试批量处理模式"""
    print("\n🧪 测试批量处理模式...")
    
    config = load_config()
    analyzer = NewsAnalyzer(config)
    
    # 创建多条测试新闻
    test_news_list = []
    for i in range(3):
        news = TEST_NEWS.copy()
        news['title'] = f"测试新闻{i+1}：{TEST_NEWS['title']}"
        test_news_list.append(news)
    
    try:
        results = await analyzer.batch_analyze(test_news_list)
        
        print(f"📊 批量分析结果: 成功分析 {len(results)} 条新闻")
        
        # 检查使用的模型
        models_used = []
        for result in results:
            analysis = result.get('analysis', {})
            model_used = analysis.get('model_used', 'unknown')
            models_used.append(model_used)
        
        print("🤖 使用的模型:")
        for i, model in enumerate(models_used):
            print(f"  新闻{i+1}: {model}")
        
        # 验证主要使用 Gemini
        gemini_count = sum(1 for model in models_used if 'gemini' in model.lower())
        
        if gemini_count > 0:
            print(f"✅ 批量处理正常，主要使用 Gemini 模型 ({gemini_count}/{len(results)})")
            return True
        else:
            print("⚠️ 批量处理中未使用主要模型 Gemini")
            return False
            
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始 AI 模型优先级测试")
    print("=" * 60)
    
    # 设置日志级别
    logging.getLogger('analyzer').setLevel(logging.WARNING)  # 减少日志输出
    
    test_results = []
    
    # 1. 测试模型配置
    config_ok = await test_model_configuration()
    test_results.append(("模型配置检查", config_ok))
    
    if not config_ok:
        print("\n❌ 模型配置错误，请检查配置文件")
        return
    
    # 2. 测试所有模型可用性
    models_ok = await test_all_models_available()
    test_results.append(("模型可用性检查", models_ok))
    
    # 3. 测试主要模型
    primary_ok = await test_primary_model()
    test_results.append(("主要模型测试", primary_ok))
    
    # 4. 测试备用模型切换
    fallback_ok = await test_fallback_mechanism()
    test_results.append(("备用模型切换", fallback_ok))
    
    # 5. 测试批量处理
    batch_ok = await test_batch_processing()
    test_results.append(("批量处理测试", batch_ok))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n🎯 新的模型优先级顺序:")
    print(f"  1️⃣ 主要模型: Gemini (gemini-2.5-pro)")
    print(f"  2️⃣ 备用模型: Kimi (kimi-k2-0711-preview)")
    print(f"  3️⃣ 保留模型: DeepSeek (deepseek-reasoner)")
    
    print(f"\n🔄 切换机制:")
    print(f"  • Gemini 成功 → 使用 Gemini 分析")
    print(f"  • Gemini 失败 → 自动切换到 Kimi")
    print(f"  • 两者都失败 → 报告错误（模拟分析已禁用）")
    
    if all_passed:
        print("\n🎉 所有测试通过！模型优先级配置正常工作")
    else:
        print("\n⚠️ 部分测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    asyncio.run(main())
