#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻分析模块 - 多模型支持

功能：支持Google Gemini和DeepSeek双模型分析
特点：异步批量处理，智能降级，自包含
"""

import asyncio
import aiohttp
import json
import logging
import hashlib
import sqlite3
import time
import random
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from config import get_config


class NewsAnalyzer:
    """新闻分析器类 - 多模型支持"""

    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('analyzer')
        self.cache = {}  # 简单内存缓存
        self.db_path = Path(__file__).parent / "news_analysis.db"

        # 多模型配置
        self.primary_provider = get_config(config, 'analyzer.primary_provider', 'gemini')
        self.fallback_provider = get_config(config, 'analyzer.fallback_provider', 'deepseek')
        self.allow_mock_analysis = get_config(config, 'analyzer.allow_mock_analysis', False)

        # API 速率限制控制
        kimi_max_concurrent = get_config(config, 'analyzer.kimi.max_concurrent', 1)
        kimi_request_interval = get_config(config, 'analyzer.kimi.request_interval', 2.0)

        self._kimi_semaphore = asyncio.Semaphore(kimi_max_concurrent)  # Kimi API 并发限制
        self._last_kimi_request_time = 0  # 上次 Kimi 请求时间
        self._kimi_min_interval = kimi_request_interval  # Kimi 请求最小间隔（秒）

        # 初始化分析结果数据库
        self.init_analysis_database()

        self.logger.info(f"新闻分析器初始化完成")
        self.logger.info(f"主要模型: {self.primary_provider}")
        self.logger.info(f"备用模型: {self.fallback_provider}")
        self.logger.info(f"允许模拟分析: {'是' if self.allow_mock_analysis else '否'}")

        if not self.allow_mock_analysis:
            self.logger.info("🚫 模拟分析已禁用，所有分析必须使用真实AI模型")

    def init_analysis_database(self):
        """初始化分析结果数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id INTEGER PRIMARY KEY,
                    news_hash TEXT UNIQUE,
                    news_title TEXT,
                    news_content TEXT,
                    analysis_result TEXT,
                    model_used TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()
    
    async def analyze_news(self, news):
        """分析单条新闻 - 多模型支持，带去重和本地存储"""
        news_hash = self._get_news_hash(news)

        # 首先检查数据库中是否已有分析结果
        existing_result = self._get_existing_analysis(news_hash)
        if existing_result:
            self.logger.debug(f"使用已存储的分析结果: {news['title'][:30]}...")
            return existing_result

        # 检查内存缓存
        cache_key = self._get_cache_key(news)
        if cache_key in self.cache:
            self.logger.debug(f"使用内存缓存结果: {news['title'][:30]}...")
            return self.cache[cache_key]

        # 进行新的分析
        result = None

        # 尝试主要模型
        try:
            self.logger.info(f"尝试使用主要模型: {self.primary_provider}")
            if self.primary_provider == 'gemini':
                result = await self._call_gemini_api(news)
            elif self.primary_provider == 'deepseek':
                result = await self._call_deepseek_api(news)
            elif self.primary_provider == 'kimi':
                result = await self._call_kimi_api(news)

            if result:
                self.logger.info(f"主要模型 {self.primary_provider} 分析成功")
                # 保存到数据库和缓存
                self._save_analysis_result(news_hash, news, result)
                self.cache[cache_key] = result
                return result
            else:
                self.logger.warning(f"主要模型 {self.primary_provider} 返回空结果")
        except Exception as e:
            self.logger.error(f"主要模型 {self.primary_provider} 调用失败: {e}")
            import traceback
            self.logger.debug(f"详细错误信息: {traceback.format_exc()}")

        # 尝试备用模型
        try:
            self.logger.info(f"尝试使用备用模型: {self.fallback_provider}")
            if self.fallback_provider == 'deepseek':
                result = await self._call_deepseek_api(news)
            elif self.fallback_provider == 'gemini':
                result = await self._call_gemini_api(news)
            elif self.fallback_provider == 'kimi':
                result = await self._call_kimi_api(news)

            if result:
                self.logger.info(f"备用模型 {self.fallback_provider} 分析成功")
                result['model_used'] = f"{self.fallback_provider} (fallback)"
                # 保存到数据库和缓存
                self._save_analysis_result(news_hash, news, result)
                self.cache[cache_key] = result
                return result
            else:
                self.logger.warning(f"备用模型 {self.fallback_provider} 返回空结果")
        except Exception as e:
            self.logger.error(f"备用模型 {self.fallback_provider} 调用失败: {e}")
            import traceback
            self.logger.debug(f"详细错误信息: {traceback.format_exc()}")

        # 所有模型都失败的处理
        if self.allow_mock_analysis:
            self.logger.warning("所有AI模型都失败，使用模拟分析")
            result = self._mock_analysis(news)
            # 保存模拟结果到数据库和缓存
            self._save_analysis_result(news_hash, news, result)
            self.cache[cache_key] = result
            return result
        else:
            # 禁用模拟分析，抛出异常
            error_msg = f"所有AI模型都失败，且模拟分析已禁用，无法分析新闻: {news.get('title', '')[:50]}..."
            self.logger.error(error_msg)
            raise Exception(error_msg)
    
    async def batch_analyze(self, news_list):
        """批量分析新闻 - 针对 Kimi API 优化"""
        batch_size = get_config(self.config, 'analyzer.batch_size', 3)  # 减小批次大小
        results = []

        # 检查主要模型是否为 Kimi，如果是则使用顺序处理
        if self.primary_provider == 'kimi':
            self.logger.info(f"使用 Kimi 模型，采用顺序处理模式以避免速率限制")

            for i, news in enumerate(news_list):
                self.logger.info(f"分析新闻 {i + 1}/{len(news_list)}: {news.get('title', '')[:50]}...")

                try:
                    result = await self.analyze_news(news)
                    results.append({
                        'news': news,
                        'analysis': result
                    })
                    self.logger.info(f"新闻 {i + 1} 分析完成")

                    # 在新闻之间添加小延迟
                    if i < len(news_list) - 1:
                        await asyncio.sleep(0.5)

                except Exception as e:
                    self.logger.error(f"分析失败: {e}")
                    self.logger.warning(f"跳过无法分析的新闻: {news.get('title', '')[:50]}...")
                    continue
        else:
            # 非 Kimi 模型使用原有的并发处理
            for i in range(0, len(news_list), batch_size):
                batch = news_list[i:i + batch_size]
                self.logger.info(f"分析批次 {i//batch_size + 1}: {len(batch)} 条新闻")

                # 并发分析当前批次
                tasks = [self.analyze_news(news) for news in batch]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理结果
                for j, result in enumerate(batch_results):
                    if isinstance(result, Exception):
                        self.logger.error(f"分析失败: {result}")
                        self.logger.warning(f"跳过无法分析的新闻: {batch[j].get('title', '')[:50]}...")
                        continue

                    results.append({
                        'news': batch[j],
                        'analysis': result
                    })

        return results

    async def _call_gemini_api(self, news):
        """调用Google Gemini API"""
        api_key = get_config(self.config, 'analyzer.gemini.api_key', '')
        if not api_key:
            raise Exception("Gemini API密钥未配置")

        api_url = get_config(self.config, 'analyzer.gemini.api_url',
                           'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent')

        # 检查是否是思考模型
        is_thinking_model = 'gemini-2.5-pro' in api_url

        if is_thinking_model:
            # 使用专门的思考模型分析器
            from thinking_model_analyzer import ThinkingModelAnalyzer

            gemini_config = get_config(self.config, 'analyzer.gemini', {})
            thinking_analyzer = ThinkingModelAnalyzer(gemini_config)
            system_prompt = get_config(self.config, 'analyzer.system_prompt', '')

            self.logger.info("使用思考模型分析器处理Gemini 2.5 Pro")
            return await thinking_analyzer.analyze_with_thinking_model(news, system_prompt)

        # 非思考模型的常规处理
        timeout = get_config(self.config, 'analyzer.timeout', 60)
        max_tokens = get_config(self.config, 'analyzer.gemini.max_tokens', 1000)
        temperature = get_config(self.config, 'analyzer.gemini.temperature', 0.1)

        # 获取系统提示词
        system_prompt = get_config(self.config, 'analyzer.system_prompt', '')

        # 构建用户输入
        user_prompt = f"""【财联社新闻原文】

标题：{news['title']}

内容：{news['content']}

---

请按照您的专业工作流程，对上述财联社新闻进行完整的量化投研分析，并严格按照预期结果的Markdown格式生成报告。"""

        headers = {
            'Content-Type': 'application/json',
            'x-goog-api-key': api_key
        }

        # 构建生成配置
        generation_config = {
            "temperature": temperature,
            "maxOutputTokens": max_tokens
        }

        # 添加额外的配置参数（如果存在）
        topP = get_config(self.config, 'analyzer.gemini.topP', None)
        topK = get_config(self.config, 'analyzer.gemini.topK', None)

        if topP is not None:
            generation_config["topP"] = topP
        if topK is not None:
            generation_config["topK"] = topK

        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": f"{system_prompt}\n\n{user_prompt}"
                        }
                    ]
                }
            ],
            "generationConfig": generation_config
        }

        # 使用代理连接
        connector = aiohttp.TCPConnector()
        proxy_url = get_config(self.config, 'analyzer.gemini.proxy_url', "http://127.0.0.1:7897")

        # 对于思考模型，使用更长的超时时间
        thinking_timeout = get_config(self.config, 'analyzer.gemini.thinking_timeout', 600)
        actual_timeout = thinking_timeout if 'gemini-2.5-pro' in api_url else timeout

        async with aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=actual_timeout)
        ) as session:
            async with session.post(
                api_url,
                headers=headers,
                json=payload,
                proxy=proxy_url
            ) as response:
                if response.status == 200:
                    result = await response.json()

                    # 解析Gemini响应 - 特别处理思考模型
                    if 'candidates' in result and len(result['candidates']) > 0:
                        candidate = result['candidates'][0]
                        finish_reason = candidate.get('finishReason', '')

                        self.logger.info(f"Gemini响应完成原因: {finish_reason}")

                        # 检查使用情况统计
                        if 'usageMetadata' in result:
                            usage = result['usageMetadata']
                            thoughts_tokens = usage.get('thoughtsTokenCount', 0)
                            total_tokens = usage.get('totalTokenCount', 0)
                            self.logger.info(f"思考tokens: {thoughts_tokens}, 总tokens: {total_tokens}")

                        # 检查不同的响应格式
                        content_text = None

                        # 格式1: 标准格式 content.parts[].text
                        if 'content' in candidate and 'parts' in candidate['content']:
                            parts = candidate['content']['parts']
                            if len(parts) > 0 and 'text' in parts[0]:
                                content_text = parts[0]['text'].strip()
                                self.logger.info(f"从parts中提取到文本: {len(content_text)} 字符")

                        # 格式2: 思考模型可能的特殊格式
                        elif 'content' in candidate:
                            content_obj = candidate['content']
                            if isinstance(content_obj, dict):
                                # 检查所有可能的文本字段
                                text_fields = ['text', 'output', 'response', 'answer', 'result']
                                for field in text_fields:
                                    if field in content_obj and isinstance(content_obj[field], str):
                                        content_text = content_obj[field].strip()
                                        self.logger.info(f"从{field}字段提取到文本: {len(content_text)} 字符")
                                        break

                                # 如果还是没有，尝试查找任何字符串值
                                if not content_text:
                                    for key, value in content_obj.items():
                                        if isinstance(value, str) and len(value) > 10:
                                            content_text = value.strip()
                                            self.logger.info(f"从{key}字段提取到文本: {len(content_text)} 字符")
                                            break

                        # 格式3: 直接在candidate中查找文本
                        if not content_text:
                            text_fields = ['text', 'output', 'response', 'answer', 'result']
                            for field in text_fields:
                                if field in candidate and isinstance(candidate[field], str):
                                    content_text = candidate[field].strip()
                                    self.logger.info(f"从candidate.{field}提取到文本: {len(content_text)} 字符")
                                    break

                        if content_text:
                            # 检查分析完整性
                            if len(content_text) < 500:
                                self.logger.warning(f"Gemini分析结果过短({len(content_text)}字符)，可能不完整")
                                raise Exception(f"Gemini分析结果过短({len(content_text)}字符)，可能被截断")

                            # 检查是否包含关键分析要素
                            required_elements = ["好公司", "好行业", "好价格", "好时机"]
                            missing_elements = [elem for elem in required_elements if elem not in content_text]
                            if len(missing_elements) > 2:
                                self.logger.warning(f"Gemini分析缺少关键要素: {missing_elements}")
                                raise Exception(f"Gemini分析不完整，缺少关键要素: {missing_elements}")

                            # 返回完整的分析结果
                            return {
                                "markdown_report": content_text,
                                "sentiment": self._extract_sentiment(content_text),
                                "entities": self._extract_entities(content_text),
                                "summary": self._extract_summary(content_text),
                                "confidence": 0.9,
                                "model_used": "gemini-2.5-pro"
                            }
                        else:
                            # 对于思考模型，如果finishReason是MAX_TOKENS但没有文本，可能需要等待
                            if finish_reason == 'MAX_TOKENS':
                                self.logger.warning(f"Gemini 2.5 Pro思考模型可能还在处理中，完整响应: {result}")
                                raise Exception("Gemini 2.5 Pro思考模型响应不完整，可能需要更长等待时间")
                            else:
                                self.logger.warning(f"Gemini响应中未找到文本内容: {result}")
                                raise Exception("Gemini响应中未找到文本内容")

                    raise Exception("Gemini响应格式异常")
                else:
                    error_text = await response.text()
                    raise Exception(f"Gemini API调用失败: {response.status} - {error_text}")

    async def _call_deepseek_api(self, news):
        """调用DeepSeek API"""
        api_key = get_config(self.config, 'analyzer.deepseek.api_key', '')
        if not api_key:
            raise Exception("DeepSeek API密钥未配置")

        api_url = get_config(self.config, 'analyzer.deepseek.api_url',
                           'https://api.deepseek.com/chat/completions')
        timeout = get_config(self.config, 'analyzer.timeout', 60)
        max_tokens = get_config(self.config, 'analyzer.deepseek.max_tokens', 1000)
        temperature = get_config(self.config, 'analyzer.deepseek.temperature', 0.1)
        model = get_config(self.config, 'analyzer.deepseek.model', 'deepseek-chat')

        # 获取完整的A股量化投研分析师提示词
        system_prompt = get_config(self.config, 'analyzer.system_prompt', '')

        # 构建用户输入，按照提示词要求的格式
        user_prompt = f"""【财联社新闻原文】

标题：{news['title']}

内容：{news['content']}

---

请按照您的专业工作流程，对上述财联社新闻进行完整的量化投研分析，并严格按照预期结果的Markdown格式生成报告。"""

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }

            data = {
                "model": model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "max_tokens": max_tokens,
                "temperature": temperature
            }

            async with session.post(api_url, json=data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    self.logger.info(f"DeepSeek API响应状态: 200")
                    self.logger.info(f"DeepSeek API响应keys: {list(result.keys())}")

                    if "choices" not in result or len(result["choices"]) == 0:
                        self.logger.error(f"DeepSeek API响应格式异常: {result}")
                        raise Exception("DeepSeek API响应格式异常：没有choices")

                    choice = result["choices"][0]
                    self.logger.info(f"Choice keys: {list(choice.keys())}")

                    message = choice.get("message", {})
                    self.logger.info(f"Message keys: {list(message.keys())}")

                    content = message.get("content", "")
                    reasoning_content = message.get("reasoning_content", "")

                    self.logger.info(f"DeepSeek content长度: {len(content)}")
                    self.logger.info(f"DeepSeek reasoning_content长度: {len(reasoning_content)}")

                    # 对于DeepSeek Reasoner，如果content为空但有reasoning_content，使用reasoning_content
                    if len(content) == 0 and len(reasoning_content) > 0:
                        self.logger.info("DeepSeek Reasoner: 使用reasoning_content作为分析结果")
                        content = reasoning_content
                    elif len(content) == 0:
                        self.logger.warning(f"DeepSeek返回空内容，完整响应: {result}")

                    self.logger.info(f"DeepSeek分析完成，最终内容长度: {len(content)}")

                    # 检查分析完整性
                    if len(content) < 500:
                        self.logger.warning(f"DeepSeek分析结果过短({len(content)}字符)，可能不完整")
                        raise Exception(f"DeepSeek分析结果过短({len(content)}字符)，可能被截断")

                    # 检查是否包含关键分析要素
                    required_elements = ["好公司", "好行业", "好价格", "好时机"]
                    missing_elements = [elem for elem in required_elements if elem not in content]
                    if len(missing_elements) > 2:
                        self.logger.warning(f"DeepSeek分析缺少关键要素: {missing_elements}")
                        raise Exception(f"DeepSeek分析不完整，缺少关键要素: {missing_elements}")

                    # 返回完整的Markdown报告内容
                    return {
                        "markdown_report": content,
                        "sentiment": self._extract_sentiment(content),
                        "entities": self._extract_entities(content),
                        "summary": self._extract_summary(content),
                        "confidence": 0.9,
                        "model_used": "deepseek"
                    }
                else:
                    error_text = await response.text()
                    self.logger.error(f"DeepSeek API调用失败: {response.status} - {error_text}")
                    raise Exception(f"DeepSeek API调用失败: {response.status} - {error_text}")

    async def _call_kimi_api(self, news):
        """调用Kimi API - 带速率限制和重试机制"""
        api_key = get_config(self.config, 'analyzer.kimi.api_key', '')
        if not api_key:
            raise Exception("Kimi API密钥未配置")

        api_url = get_config(self.config, 'analyzer.kimi.api_url',
                           'https://api.moonshot.cn/v1/chat/completions')
        timeout = get_config(self.config, 'analyzer.timeout', 60)
        max_tokens = get_config(self.config, 'analyzer.kimi.max_tokens', 4000)
        temperature = get_config(self.config, 'analyzer.kimi.temperature', 0.6)
        model = get_config(self.config, 'analyzer.kimi.model', 'kimi-k2-0711-preview')
        max_retries = get_config(self.config, 'analyzer.max_retries', 3)

        # 获取完整的A股量化投研分析师提示词
        system_prompt = get_config(self.config, 'analyzer.system_prompt', '')

        # 构建用户输入，按照提示词要求的格式
        user_prompt = f"""【财联社新闻原文】

标题：{news['title']}

内容：{news['content']}

---

请按照系统提示词的要求，对上述新闻进行完整的8步投资分析。"""

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }

        payload = {
            "model": model,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ],
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        # 使用信号量控制并发和重试机制
        async with self._kimi_semaphore:
            for attempt in range(max_retries):
                try:
                    # 速率限制：确保请求间隔
                    current_time = time.time()
                    time_since_last_request = current_time - self._last_kimi_request_time
                    if time_since_last_request < self._kimi_min_interval:
                        wait_time = self._kimi_min_interval - time_since_last_request
                        self.logger.info(f"Kimi API 速率限制：等待 {wait_time:.2f} 秒")
                        await asyncio.sleep(wait_time)

                    self._last_kimi_request_time = time.time()

                    self.logger.info(f"调用Kimi API (尝试 {attempt + 1}/{max_retries}): {api_url}")
                    self.logger.debug(f"Kimi请求参数: model={model}, max_tokens={max_tokens}, temperature={temperature}")

                    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                        async with session.post(api_url, headers=headers, json=payload) as response:
                            if response.status == 200:
                                result = await response.json()
                                self.logger.debug(f"Kimi原始响应: {result}")

                                if 'choices' in result and len(result['choices']) > 0:
                                    content = result['choices'][0]['message']['content']

                                    self.logger.info(f"Kimi分析完成，内容长度: {len(content)}")

                                    # 检查分析完整性
                                    if len(content) < 500:
                                        self.logger.warning(f"Kimi分析结果过短({len(content)}字符)，可能不完整")
                                        raise Exception(f"Kimi分析结果过短({len(content)}字符)，可能被截断")

                                    # 检查是否包含关键分析要素
                                    required_elements = ["事件概述", "关键特征", "行为改变", "供需错配", "行业映射", "标的筛选", "财务与估值", "投资结论"]
                                    missing_elements = [elem for elem in required_elements if elem not in content]
                                    if len(missing_elements) > 3:
                                        self.logger.warning(f"Kimi分析缺少关键要素: {missing_elements}")
                                        raise Exception(f"Kimi分析不完整，缺少关键要素: {missing_elements}")

                                    # 返回完整的Markdown报告内容
                                    return {
                                        "markdown_report": content,
                                        "sentiment": self._extract_sentiment(content),
                                        "entities": self._extract_entities(content),
                                        "summary": self._extract_summary(content),
                                        "confidence": 0.9,
                                        "model_used": "kimi"
                                    }
                                else:
                                    raise Exception("Kimi响应中未找到choices字段")

                            elif response.status == 429:
                                # 处理速率限制错误
                                error_text = await response.text()
                                self.logger.warning(f"Kimi API 速率限制 (尝试 {attempt + 1}/{max_retries}): {error_text}")

                                # 解析错误信息中的等待时间
                                wait_time = self._parse_retry_after(error_text)
                                if wait_time is None:
                                    wait_time = min(2 ** attempt, 10)  # 指数退避，最大10秒

                                if attempt < max_retries - 1:
                                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                                    await asyncio.sleep(wait_time)
                                    continue
                                else:
                                    raise Exception(f"Kimi API 速率限制，已达到最大重试次数: {error_text}")

                            else:
                                # 其他HTTP错误
                                error_text = await response.text()
                                self.logger.error(f"Kimi API调用失败: {response.status} - {error_text}")
                                if attempt < max_retries - 1:
                                    wait_time = min(2 ** attempt, 5)  # 指数退避
                                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                                    await asyncio.sleep(wait_time)
                                    continue
                                else:
                                    raise Exception(f"Kimi API调用失败: {response.status} - {error_text}")

                except asyncio.TimeoutError:
                    self.logger.warning(f"Kimi API 请求超时 (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        wait_time = min(2 ** attempt, 5)
                        self.logger.info(f"等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        raise Exception("Kimi API 请求超时，已达到最大重试次数")

                except Exception as e:
                    self.logger.error(f"Kimi API 调用异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        wait_time = min(2 ** attempt, 5)
                        self.logger.info(f"等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        raise

            # 如果所有重试都失败了
            raise Exception(f"Kimi API 调用失败，已尝试 {max_retries} 次")

    def _parse_retry_after(self, error_text):
        """解析错误信息中的重试等待时间"""
        try:
            import re
            # 尝试从错误信息中提取等待时间
            match = re.search(r'after (\d+) seconds', error_text)
            if match:
                return int(match.group(1)) + 0.5  # 额外增加0.5秒缓冲
            return None
        except:
            return None

    def _mock_analysis(self, news):
        """模拟分析结果"""
        mock_report = f"""# 财经新闻核心标的分析报告

## 核心事件概述
- **新闻来源**: 财联社
- **事件摘要**: {news['title']}

## 多维度评估
- **核心实体**: [模拟实体]
- **情感倾向**: 中性
- **事件分类**: 市场传闻
- **宏观影响**: 个股级影响，短期

## 直接影响标的清单与分析
| 股票代码 | 公司简称 | 核心逻辑链 | 短期(周)预测 | 中期(季)预测 | 关键观察点 |
|---|---|---|---|---|---|
| 数据缺失 | 数据缺失 | 数据缺失 | 数据缺失 | 数据缺失 | 数据缺失 |

*免责声明: 本报告仅分析与新闻直接相关的标的，不构成投资建议。*

## 分析依据
- **新闻输入**: {news['title']}
- **数据来源**: 模拟数据"""

        return {
            "markdown_report": mock_report,
            "sentiment": "中性",
            "entities": ["模拟实体"],
            "summary": f"这是对新闻《{news['title'][:20]}...》的模拟分析",
            "confidence": 0.7,
            "model_used": "mock"
        }

    def _extract_sentiment(self, content):
        """从Markdown内容中提取情感倾向"""
        import re

        # 查找情感倾向
        sentiment_match = re.search(r'情感倾向.*?[:：]\s*([正面负面中性]+)', content)
        if sentiment_match:
            return sentiment_match.group(1)
        return "中性"

    def _extract_entities(self, content):
        """从Markdown内容中提取核心实体"""
        import re

        # 查找核心实体
        entities_match = re.search(r'核心实体.*?[:：]\s*\[(.*?)\]', content)
        if entities_match:
            entities_str = entities_match.group(1)
            return [entity.strip() for entity in entities_str.split(',') if entity.strip()]
        return []

    def _extract_summary(self, content):
        """从Markdown内容中提取事件摘要"""
        import re

        # 查找事件摘要
        summary_match = re.search(r'事件摘要.*?[:：]\s*([^\n]+)', content)
        if summary_match:
            return summary_match.group(1).strip()

        # 如果没找到，返回第一行标题
        lines = content.split('\n')
        for line in lines:
            if line.strip() and not line.startswith('#'):
                return line.strip()[:100]

        return "财经新闻分析报告"
    
    def _get_cache_key(self, news):
        """生成缓存键"""
        content = f"{news['title']}_{news['timestamp']}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def _get_news_hash(self, news):
        """生成新闻哈希值（仅基于标题，用于去重）"""
        # 只使用标题生成哈希，避免相同新闻因内容细微差异而重复分析
        title = news.get('title', '').strip()
        # 清理标题中的特殊字符和多余空格，确保一致性
        title = ' '.join(title.split())
        return hashlib.md5(title.encode('utf-8')).hexdigest()

    def _get_existing_analysis(self, news_hash):
        """从数据库获取已存在的分析结果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT analysis_result FROM analysis_results WHERE news_hash = ?',
                    (news_hash,)
                )
                row = cursor.fetchone()
                if row:
                    return json.loads(row[0])
        except Exception as e:
            self.logger.error(f"获取已存在分析结果失败: {e}")
        return None

    def _save_analysis_result(self, news_hash, news, result):
        """保存分析结果到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO analysis_results
                    (news_hash, news_title, news_content, analysis_result, model_used)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    news_hash,
                    news.get('title', ''),
                    news.get('content', ''),
                    json.dumps(result, ensure_ascii=False),
                    result.get('model_used', 'unknown')
                ))
                conn.commit()
                self.logger.debug(f"分析结果已保存: {news.get('title', '')[:30]}...")
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {e}")
