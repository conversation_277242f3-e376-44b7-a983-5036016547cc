#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻爬虫模块（重构版）

功能：统一管理多个新闻源，支持异步并发获取
特点：模块化设计，易于扩展新的新闻源
"""

import sqlite3
import hashlib
import logging
from datetime import datetime
from pathlib import Path

from config import get_config
from news_sources import MultiSourceNewsManager, NewsItem


class NewsCrawler:
    """新闻爬虫类（重构版）"""

    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('crawler')
        self.db_path = Path(__file__).parent / "news.db"

        # 初始化多源新闻管理器
        crawler_config = get_config(config, 'crawler', {})
        news_sources_config = get_config(config, 'news_sources', {})

        manager_config = {
            'global_timeout': get_config(crawler_config, 'global_timeout', 60),
            'max_concurrent': get_config(crawler_config, 'max_concurrent', 3),
            'news_sources': news_sources_config
        }

        self.news_manager = MultiSourceNewsManager(manager_config)
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            # 创建表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS news (
                    id INTEGER PRIMARY KEY,
                    title TEXT NOT NULL,
                    content TEXT,
                    url TEXT,
                    timestamp TEXT,
                    source TEXT,
                    category TEXT,
                    hash TEXT UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 检查并添加新字段（兼容旧数据库）
            try:
                conn.execute('ALTER TABLE news ADD COLUMN source TEXT')
            except sqlite3.OperationalError:
                pass  # 字段已存在

            try:
                conn.execute('ALTER TABLE news ADD COLUMN category TEXT')
            except sqlite3.OperationalError:
                pass  # 字段已存在

            conn.commit()

    async def get_latest_news(self):
        """获取最新新闻（多源）"""
        try:
            self.logger.info("开始获取多源新闻...")

            # 使用多源管理器获取新闻
            news_items = await self.news_manager.fetch_all_news()

            if not news_items:
                self.logger.warning("未获取到任何新闻")
                return []

            # 转换为字典格式并过滤新新闻
            new_news = []
            duplicate_count = 0

            for news_item in news_items:
                news_dict = news_item.to_dict()

                if not self._is_duplicate(news_dict):
                    self._save_news(news_dict)
                    new_news.append(news_dict)
                    self.logger.debug(f"新新闻: {news_dict['title'][:50]}...")
                else:
                    duplicate_count += 1
                    self.logger.debug(f"重复新闻: {news_dict['title'][:50]}...")

            self.logger.info(f"多源获取到 {len(news_items)} 条新闻，其中 {len(new_news)} 条为新新闻，{duplicate_count} 条重复")
            return new_news

        except Exception as e:
            self.logger.error(f"获取多源新闻失败: {e}")
            return []
    
    async def get_sources_info(self):
        """获取新闻源信息"""
        return self.news_manager.get_sources_info()

    async def test_all_sources(self):
        """测试所有新闻源"""
        return await self.news_manager.test_all_sources()





    def _generate_hash(self, news):
        """生成新闻哈希值（仅基于标题，避免URL参数导致的重复）"""
        # 只使用标题生成哈希，避免相同新闻因URL不同而被重复爬取
        title = news.get('title', '').strip()
        # 清理标题中的特殊字符和多余空格
        title = ' '.join(title.split())
        return hashlib.md5(title.encode('utf-8')).hexdigest()
    
    def _is_duplicate(self, news):
        """检查是否重复新闻"""
        news_hash = self._generate_hash(news)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute('SELECT 1 FROM news WHERE hash = ?', (news_hash,))
            return cursor.fetchone() is not None
    
    def _save_news(self, news):
        """保存新闻到数据库"""
        news_hash = self._generate_hash(news)

        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT OR IGNORE INTO news (title, content, url, timestamp, source, category, hash)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                news['title'],
                news['content'],
                news['url'],
                news['timestamp'],
                news.get('source', ''),
                news.get('category', ''),
                news_hash
            ))
            conn.commit()
