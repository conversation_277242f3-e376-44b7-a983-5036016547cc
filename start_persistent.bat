@echo off
chcp 65001 >nul
title AI新闻分析系统 - 持久化运行

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🧠 AI新闻分析系统 - 持久化运行版本                                          ║
echo ║                                                                              ║
echo ║    🚀 快速启动选项:                                                           ║
echo ║      1. 测试模式 (单次运行)                                                   ║
echo ║      2. 持久化运行模式 ⭐                                                     ║
echo ║      3. 直接启动持久化运行                                                    ║
echo ║      4. 查看配置信息                                                          ║
echo ║      5. 退出                                                                 ║
echo ║                                                                              ║
echo ║    💡 推荐: 选择2或3进行持久化运行                                            ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:menu
set /p choice="请选择运行模式 (1-5): "

if "%choice%"=="1" goto test_mode
if "%choice%"=="2" goto run_mode
if "%choice%"=="3" goto direct_run
if "%choice%"=="4" goto config_mode
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
goto menu

:test_mode
echo.
echo 🧪 启动测试模式...
echo 📝 这将运行一次完整的新闻分析流程
echo.
python start_persistent.py --mode test
echo.
echo 📊 测试完成！如果成功，可以选择持久化运行模式
pause
goto menu

:run_mode
echo.
echo 🔄 启动持久化运行模式...
echo 💡 提示: 按 Ctrl+C 停止系统
echo 🔄 系统将每30分钟自动运行一次
echo 🌙 夜间23:00-7:00自动暂停
echo.
python start_persistent.py --mode run
echo.
echo 🛑 持久化运行已停止
pause
goto menu

:direct_run
echo.
echo 🚀 直接启动持久化运行...
echo 💡 这是最常用的运行方式
echo 🔄 系统将持续运行，按 Ctrl+C 停止
echo.
title AI新闻分析系统 - 正在运行
python main_persistent.py
echo.
echo 🛑 系统已停止运行
title AI新闻分析系统 - 已停止
pause
goto menu

:config_mode
echo.
echo 📋 显示配置信息...
python start_persistent.py --config --no-banner
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用AI新闻分析系统！
echo 💡 如需重新启动，请再次运行此批处理文件
timeout /t 2 >nul
exit /b 0
