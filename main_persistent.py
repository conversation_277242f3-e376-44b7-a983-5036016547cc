#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻分析系统 - 持久化运行版本

功能：
1. 定时自动运行新闻分析
2. 智能调度和错误处理
3. 状态监控和报告
4. 优雅停止和重启
"""

import asyncio
import signal
import sys
import time
import logging
from datetime import datetime, timedelta
from typing import Optional

from config import load_config, setup_logging
from crawler import NewsCrawler
from analyzer import NewsAnalyzer
from pusher import EmailPusher


class PersistentNewsAnalysisSystem:
    """持久化新闻分析系统"""
    
    def __init__(self):
        self.config = None
        self.logger = None
        self.crawler = None
        self.analyzer = None
        self.pusher = None
        
        # 运行状态
        self.running = False
        self.run_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_run_time = None
        self.last_success_time = None
        self.start_time = None
        
        # 调度器配置
        self.scheduler_config = {}
        self.interval_minutes = 30
        self.max_runs = 0
        self.quiet_hours = {}
        self.working_days_only = False
        self.status_report_interval = 6
        self.error_retry_delay = 5
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    async def init_system(self):
        """初始化系统"""
        try:
            # 加载配置
            self.config = load_config()
            
            # 设置日志
            self.logger = setup_logging(self.config)
            self.logger.info("🚀 持久化新闻分析系统初始化开始")
            
            # 加载调度器配置
            self.scheduler_config = self.config.get('scheduler', {})
            self.interval_minutes = self.scheduler_config.get('interval_minutes', 30)
            self.max_runs = self.scheduler_config.get('max_runs', 0)
            self.quiet_hours = self.scheduler_config.get('quiet_hours', {})
            self.working_days_only = self.scheduler_config.get('working_days_only', False)
            self.status_report_interval = self.scheduler_config.get('status_report_interval', 6)
            self.error_retry_delay = self.scheduler_config.get('error_retry_delay', 5)
            
            # 初始化各模块
            self.crawler = NewsCrawler(self.config)
            self.analyzer = NewsAnalyzer(self.config)
            self.pusher = EmailPusher(self.config)
            
            self.start_time = datetime.now()
            self.running = True
            
            self.logger.info("✅ 持久化新闻分析系统初始化完成")
            self.logger.info(f"📅 调度配置: 每{self.interval_minutes}分钟运行一次")
            self.logger.info(f"🔄 最大运行次数: {'无限制' if self.max_runs == 0 else self.max_runs}")
            self.logger.info(f"🌙 静默时间: {self.quiet_hours.get('start_hour', 23)}:00 - {self.quiet_hours.get('end_hour', 7)}:00")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"❌ 系统初始化失败: {e}")
            else:
                print(f"❌ 系统初始化失败: {e}")
            return False
    
    async def run_single_analysis(self):
        """运行单次分析"""
        run_start_time = datetime.now()
        self.run_count += 1
        self.last_run_time = run_start_time
        
        self.logger.info(f"🔄 开始第 {self.run_count} 次新闻分析 ({run_start_time.strftime('%Y-%m-%d %H:%M:%S')})")
        
        try:
            # 1. 获取新闻
            self.logger.info("📰 获取最新新闻...")
            news_list = await self.crawler.get_latest_news()
            self.logger.info(f"📊 获取到 {len(news_list)} 条新闻")
            
            if not news_list:
                self.logger.warning("⚠️ 未获取到新闻，跳过本次分析")
                return True
            
            # 限制新闻数量
            max_news = self.scheduler_config.get('max_news_per_run', 20)
            if len(news_list) > max_news:
                news_list = news_list[:max_news]
                self.logger.info(f"📝 限制分析数量为 {max_news} 条")
            
            # 2. 分析新闻
            self.logger.info("🧠 开始AI分析...")
            analysis_results = await self.analyzer.batch_analyze(news_list)
            self.logger.info(f"✅ 完成 {len(analysis_results)} 条新闻分析")
            
            # 3. 推送结果
            if analysis_results:
                self.logger.info("📧 推送分析结果...")
                await self.pusher.send_analysis_report(analysis_results)
                self.logger.info("✅ 结果推送完成")
            
            # 记录成功
            self.success_count += 1
            self.last_success_time = datetime.now()
            
            run_duration = (datetime.now() - run_start_time).total_seconds()
            self.logger.info(f"🎉 第 {self.run_count} 次分析完成，耗时 {run_duration:.1f} 秒")
            
            # 发送状态报告（如果启用）
            if self.status_report_interval > 0 and self.run_count % self.status_report_interval == 0:
                await self._send_status_report()
            
            return True
            
        except Exception as e:
            self.error_count += 1
            run_duration = (datetime.now() - run_start_time).total_seconds()
            self.logger.error(f"❌ 第 {self.run_count} 次分析失败 (耗时 {run_duration:.1f} 秒): {e}")
            
            # 发送错误报告
            await self._send_error_report(e)
            
            return False
    
    def _should_run_now(self) -> bool:
        """判断当前是否应该运行"""
        now = datetime.now()
        
        # 检查静默时间
        if self.quiet_hours.get('enabled', False):
            start_hour = self.quiet_hours.get('start_hour', 23)
            end_hour = self.quiet_hours.get('end_hour', 7)
            current_hour = now.hour
            
            if start_hour > end_hour:  # 跨天的情况，如23:00-7:00
                if current_hour >= start_hour or current_hour < end_hour:
                    self.logger.debug(f"🌙 当前处于静默时间 ({start_hour}:00-{end_hour}:00)")
                    return False
            else:  # 同一天的情况
                if start_hour <= current_hour < end_hour:
                    self.logger.debug(f"🌙 当前处于静默时间 ({start_hour}:00-{end_hour}:00)")
                    return False
        
        # 检查工作日限制
        if self.working_days_only:
            if now.weekday() >= 5:  # 周六=5, 周日=6
                self.logger.debug("📅 当前为周末，跳过运行")
                return False
        
        return True
    
    async def _send_status_report(self):
        """发送状态报告"""
        try:
            uptime = datetime.now() - self.start_time
            success_rate = (self.success_count / self.run_count * 100) if self.run_count > 0 else 0
            
            status_report = {
                'news': {
                    'title': f'📊 新闻分析系统状态报告 - 第{self.run_count}次运行',
                    'content': f'''系统运行状态良好！

📈 运行统计:
- 总运行次数: {self.run_count}
- 成功次数: {self.success_count}
- 失败次数: {self.error_count}
- 成功率: {success_rate:.1f}%

⏰ 时间信息:
- 系统启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
- 运行时长: {str(uptime).split('.')[0]}
- 上次运行: {self.last_run_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_run_time else '无'}
- 上次成功: {self.last_success_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_success_time else '无'}

⚙️ 配置信息:
- 运行间隔: {self.interval_minutes} 分钟
- 最大运行次数: {'无限制' if self.max_runs == 0 else self.max_runs}
- 静默时间: {'启用' if self.quiet_hours.get('enabled') else '禁用'}
- 仅工作日: {'是' if self.working_days_only else '否'}''',
                    'source': '系统状态',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'url': 'system://status'
                },
                'analysis': {
                    'markdown_report': f'''# 📊 新闻分析系统状态报告

## 系统概况
- **运行状态**: 🟢 正常运行
- **当前版本**: 持久化版本 v1.0
- **运行模式**: 自动调度

## 性能指标
- **总运行次数**: {self.run_count}
- **成功率**: {success_rate:.1f}%
- **平均间隔**: {self.interval_minutes} 分钟
- **系统稳定性**: {'🟢 优秀' if success_rate >= 90 else '🟡 良好' if success_rate >= 70 else '🔴 需要关注'}

## 时间统计
- **系统启动**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
- **运行时长**: {str(uptime).split('.')[0]}
- **下次运行**: 约 {self.interval_minutes} 分钟后

*这是系统自动生成的状态报告，用于监控系统健康状况。*''',
                    'sentiment': '中性',
                    'entities': ['系统状态', '运行统计'],
                    'summary': f'系统已运行{self.run_count}次，成功率{success_rate:.1f}%',
                    'confidence': 1.0,
                    'model_used': 'system-status'
                }
            }
            
            await self.pusher.send_analysis_report([status_report])
            self.logger.info(f"📊 状态报告已发送 (第{self.run_count}次运行)")
            
        except Exception as e:
            self.logger.error(f"❌ 发送状态报告失败: {e}")
    
    async def _send_error_report(self, error: Exception):
        """发送错误报告"""
        try:
            error_report = {
                'news': {
                    'title': f'⚠️ 新闻分析系统错误报告 - 第{self.run_count}次运行',
                    'content': f'''系统运行遇到错误！

❌ 错误信息:
{str(error)}

📊 当前统计:
- 总运行次数: {self.run_count}
- 成功次数: {self.success_count}
- 失败次数: {self.error_count}
- 错误时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔧 系统将在 {self.error_retry_delay} 分钟后重试。''',
                    'source': '系统错误',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'url': 'system://error'
                },
                'analysis': {
                    'markdown_report': f'''# ⚠️ 系统错误报告

## 错误详情
- **错误类型**: {type(error).__name__}
- **错误信息**: {str(error)}
- **发生时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 系统状态
- **运行次数**: {self.run_count}
- **错误次数**: {self.error_count}
- **系统状态**: 🟡 遇到错误但继续运行

## 处理措施
- 系统将自动重试
- 重试间隔: {self.error_retry_delay} 分钟
- 如果错误持续，请检查系统配置

*这是系统自动生成的错误报告。*''',
                    'sentiment': '负面',
                    'entities': ['系统错误', '自动重试'],
                    'summary': f'系统第{self.run_count}次运行遇到错误: {str(error)[:100]}',
                    'confidence': 1.0,
                    'model_used': 'system-error'
                }
            }
            
            await self.pusher.send_analysis_report([error_report])
            self.logger.info("⚠️ 错误报告已发送")
            
        except Exception as e:
            self.logger.error(f"❌ 发送错误报告失败: {e}")
    
    async def run_persistent(self):
        """持久化运行主循环"""
        if not self.scheduler_config.get('enabled', True):
            self.logger.info("📴 调度器已禁用，运行单次分析后退出")
            await self.run_single_analysis()
            return
        
        self.logger.info("🔄 开始持久化运行模式")
        self.logger.info(f"⏰ 运行间隔: {self.interval_minutes} 分钟")
        
        # 启动时运行一次（如果配置允许）
        if self.scheduler_config.get('run_on_startup', True):
            if self._should_run_now():
                await self.run_single_analysis()
            else:
                self.logger.info("🌙 当前时间不适合运行，等待下次调度")
        
        # 主循环
        while self.running:
            try:
                # 检查是否达到最大运行次数
                if self.max_runs > 0 and self.run_count >= self.max_runs:
                    self.logger.info(f"✅ 已达到最大运行次数 ({self.max_runs})，系统退出")
                    break
                
                # 等待下次运行时间
                wait_seconds = self.interval_minutes * 60
                self.logger.info(f"⏳ 等待 {self.interval_minutes} 分钟后进行下次分析...")
                
                for i in range(wait_seconds):
                    if not self.running:
                        break
                    await asyncio.sleep(1)
                
                if not self.running:
                    break
                
                # 检查是否应该运行
                if self._should_run_now():
                    await self.run_single_analysis()
                else:
                    self.logger.info("🌙 当前时间不适合运行，跳过本次调度")
                
            except Exception as e:
                self.logger.error(f"❌ 主循环异常: {e}")
                self.logger.info(f"⏳ 等待 {self.error_retry_delay} 分钟后重试...")
                await asyncio.sleep(self.error_retry_delay * 60)
        
        self.logger.info("🛑 持久化运行结束")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"📡 收到信号 {signum}，准备优雅停止...")
        self.running = False
    
    async def shutdown(self):
        """优雅关闭"""
        self.logger.info("🛑 系统正在关闭...")
        
        # 发送关闭报告
        try:
            uptime = datetime.now() - self.start_time if self.start_time else timedelta(0)
            success_rate = (self.success_count / self.run_count * 100) if self.run_count > 0 else 0
            
            shutdown_report = {
                'news': {
                    'title': '🛑 新闻分析系统关闭报告',
                    'content': f'''系统已优雅关闭。

📊 最终统计:
- 总运行次数: {self.run_count}
- 成功次数: {self.success_count}
- 失败次数: {self.error_count}
- 成功率: {success_rate:.1f}%
- 总运行时长: {str(uptime).split('.')[0]}

感谢使用新闻分析系统！''',
                    'source': '系统关闭',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'url': 'system://shutdown'
                },
                'analysis': {
                    'markdown_report': f'''# 🛑 系统关闭报告

## 运行总结
- **总运行时长**: {str(uptime).split('.')[0]}
- **总运行次数**: {self.run_count}
- **系统稳定性**: {success_rate:.1f}%

## 感谢使用
新闻分析系统已安全关闭，期待下次为您服务！

*系统关闭时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*''',
                    'sentiment': '中性',
                    'entities': ['系统关闭', '运行总结'],
                    'summary': f'系统运行{str(uptime).split(".")[0]}，共{self.run_count}次分析',
                    'confidence': 1.0,
                    'model_used': 'system-shutdown'
                }
            }
            
            await self.pusher.send_analysis_report([shutdown_report])
            
        except Exception as e:
            self.logger.error(f"❌ 发送关闭报告失败: {e}")
        
        self.logger.info("✅ 系统已安全关闭")


async def main():
    """主函数"""
    system = PersistentNewsAnalysisSystem()
    
    try:
        # 初始化系统
        if not await system.init_system():
            sys.exit(1)
        
        # 运行持久化分析
        await system.run_persistent()
        
    except KeyboardInterrupt:
        system.logger.info("📡 收到键盘中断信号")
    except Exception as e:
        if system.logger:
            system.logger.error(f"❌ 系统运行异常: {e}")
        else:
            print(f"❌ 系统运行异常: {e}")
    finally:
        # 优雅关闭
        await system.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
