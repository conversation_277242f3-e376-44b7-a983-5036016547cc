#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财联社无头浏览器新闻源
"""

import asyncio
import aiohttp
import json
from typing import List, Dict, Any
from datetime import datetime
from urllib.parse import urlparse, parse_qs

from .base_source import BaseNewsSource, NewsItem

class CLSBrowserNewsSource(BaseNewsSource):
    """财联社无头浏览器新闻源"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config, "财联社")
        self.base_url = config.get('base_url', 'https://www.cls.cn/telegraph')
        self.api_url = config.get('api_url', 'https://www.cls.cn/v1/roll/get_roll_list')
        self.headless = config.get('headless', True)
        self.browser_timeout = config.get('browser_timeout', 30000)  # 30秒
        
        # 缓存签名，避免频繁启动浏览器
        self._cached_signature = None
        self._signature_timestamp = 0
        self._signature_ttl = config.get('signature_ttl', 300)  # 5分钟缓存

    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.cls.cn/telegraph',
            'Origin': 'https://www.cls.cn',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest'
        }
    
    async def fetch_news(self, session) -> List[NewsItem]:
        """获取财联社新闻（使用无头浏览器直接从DOM获取）"""
        try:
            # 直接从DOM获取新闻内容
            return await self._extract_news_from_dom()

        except Exception as e:
            self.logger.error(f"获取财联社新闻失败: {e}")
            return []
    

    
    async def _extract_news_from_dom(self) -> List[NewsItem]:
        """使用无头浏览器直接从DOM获取新闻内容"""
        try:
            from playwright.async_api import async_playwright

            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(headless=self.headless)
                context = await browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                page = await context.new_page()

                try:
                    # 访问财联社页面
                    self.logger.info(f"访问财联社页面: {self.base_url}")
                    await page.goto(self.base_url, timeout=self.browser_timeout)

                    # 等待页面加载
                    await page.wait_for_load_state('networkidle', timeout=self.browser_timeout)

                    # 等待页面完全加载
                    await asyncio.sleep(3)

                    # 查找并点击"加红"栏目
                    self.logger.info("查找并点击加红栏目...")

                    # 先等待页面完全加载
                    await asyncio.sleep(2)

                    # 尝试多种方式找到加红按钮
                    red_clicked = False

                    # 方法1: 通过精确的文本匹配查找加红按钮
                    try:
                        # 查找所有可能的加红元素
                        red_candidates = await page.query_selector_all('*')
                        for element in red_candidates:
                            try:
                                text = await element.text_content()
                                if text and text.strip() == '加红':
                                    # 检查元素是否可见和可点击
                                    if await element.is_visible() and await element.is_enabled():
                                        await element.click()
                                        red_clicked = True
                                        self.logger.info("找到并点击了加红栏目按钮")
                                        break
                            except:
                                continue
                    except:
                        pass

                    # 方法2: 通过JavaScript更精确地查找和点击
                    if not red_clicked:
                        self.logger.info("尝试JavaScript精确查找加红栏目...")
                        red_clicked = await page.evaluate("""
                            () => {
                                // 查找所有元素
                                const allElements = Array.from(document.querySelectorAll('*'));

                                // 查找包含"加红"文本的元素
                                const redElements = allElements.filter(el => {
                                    const text = el.textContent ? el.textContent.trim() : '';
                                    return text === '加红' || text.includes('加红');
                                });

                                console.log('找到', redElements.length, '个包含加红的元素');

                                // 尝试点击每个候选元素
                                for (let i = 0; i < redElements.length; i++) {
                                    const element = redElements[i];
                                    try {
                                        // 检查元素是否可见
                                        const rect = element.getBoundingClientRect();
                                        if (rect.width > 0 && rect.height > 0) {
                                            console.log('点击加红元素:', element.tagName, element.textContent);
                                            element.click();
                                            return true;
                                        }
                                    } catch (e) {
                                        console.log('点击失败:', e);
                                    }
                                }

                                return false;
                            }
                        """)

                        if red_clicked:
                            self.logger.info("JavaScript成功点击了加红栏目")

                    # 方法3: 如果还是没找到，尝试模拟键盘操作
                    if not red_clicked:
                        self.logger.info("尝试模拟用户操作查找加红栏目...")
                        # 尝试按Tab键导航到加红按钮
                        for i in range(10):
                            await page.keyboard.press('Tab')
                            await asyncio.sleep(0.2)

                            # 检查当前焦点元素是否是加红按钮
                            focused_text = await page.evaluate('document.activeElement ? document.activeElement.textContent : ""')
                            if focused_text and '加红' in focused_text:
                                await page.keyboard.press('Enter')
                                red_clicked = True
                                self.logger.info("通过键盘导航找到并点击了加红栏目")
                                break

                    if red_clicked:
                        # 等待加红栏目内容加载
                        self.logger.info("等待加红栏目内容加载...")
                        await asyncio.sleep(5)  # 增加等待时间确保内容加载

                        # 等待特定的加红内容出现
                        try:
                            await page.wait_for_function("""
                                () => {
                                    // 检查页面是否有加红相关的内容变化
                                    const content = document.body.textContent;
                                    return content && content.length > 1000;
                                }
                            """, timeout=10000)
                        except:
                            pass
                    else:
                        self.logger.warning("未能找到或点击加红栏目，将获取当前页面内容")

                    # 获取加红栏目的新闻内容
                    self.logger.info("获取加红栏目新闻内容...")

                    # 尝试多个可能的选择器来定位加红栏目内容
                    selectors_to_try = [
                        '//*[@id="__next"]/div/div[2]/div[2]/div[1]',  # 您指定的XPath
                        '//div[contains(@class, "red")]//div[contains(@class, "content")]',  # 包含red类的内容
                        '//div[@data-category="red"]',  # 数据属性为red的元素
                        '//div[contains(@class, "category-content")]',  # 栏目内容区域
                        '//div[contains(@class, "news-list")]',  # 新闻列表区域
                    ]

                    news_list = []

                    for selector in selectors_to_try:
                        try:
                            self.logger.info(f"尝试选择器: {selector}")

                            # 等待元素出现
                            if selector.startswith('//'):
                                await page.wait_for_selector(f'xpath={selector}', timeout=5000)
                                elements = await page.query_selector_all(f'xpath={selector}')
                            else:
                                await page.wait_for_selector(selector, timeout=5000)
                                elements = await page.query_selector_all(selector)

                            if elements:
                                self.logger.info(f"选择器 {selector} 找到 {len(elements)} 个元素")

                                # 获取元素内容
                                for element in elements:
                                    try:
                                        # 先获取初始内容，检查是否是加红新闻
                                        text_content = await self._extract_clean_news_content(element)
                                        if text_content and len(text_content.strip()) > 50:
                                            # 检查内容是否看起来像加红新闻
                                            if self._is_red_category_content(text_content):
                                                # 只对加红新闻进行展开操作
                                                self.logger.info("检测到加红新闻，开始展开相关按钮...")
                                                await self._handle_expand_buttons_for_red_news(page, element)

                                                # 重新获取展开后的内容
                                                text_content = await self._extract_clean_news_content(element)
                                                # 提取独立的新闻条目
                                                news_items = self._extract_individual_news(text_content)

                                                for i, news_data in enumerate(news_items[:self.max_news]):
                                                    news_item = NewsItem(
                                                        title=news_data['title'],
                                                        content=news_data['content'],
                                                        url=f"https://www.cls.cn/telegraph/red#{i+1}",
                                                        timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                                        source="财联社",
                                                        category="加红",
                                                        tags=["财联社", "加红", "重要新闻", "DOM提取"]
                                                    )
                                                    news_list.append(news_item)

                                                if news_list:
                                                    self.logger.info(f"从选择器 {selector} 提取到 {len(news_list)} 条加红新闻")
                                                    return news_list
                                    except:
                                        continue
                        except Exception as e:
                            self.logger.debug(f"选择器 {selector} 失败: {e}")
                            continue

                    # 如果上述选择器都没找到内容，使用JavaScript更深入地搜索
                    if not news_list:
                        self.logger.info("使用JavaScript深度搜索加红内容...")
                        news_data = await page.evaluate("""
                            () => {
                                const results = [];

                                // 查找所有可能包含加红新闻的元素
                                const allElements = Array.from(document.querySelectorAll('*'));

                                // 查找包含"加红"、"重要"、"紧急"等关键词的内容区域
                                const contentElements = allElements.filter(el => {
                                    const text = el.textContent || '';
                                    return text.length > 100 && (
                                        text.includes('财联社') ||
                                        text.includes('【') ||
                                        text.includes('】') ||
                                        el.className.includes('content') ||
                                        el.className.includes('news') ||
                                        el.className.includes('list')
                                    );
                                });

                                // 提取新闻内容
                                for (const element of contentElements) {
                                    const text = element.textContent || '';
                                    if (text.length > 50) {
                                        // 按行分割文本
                                        const lines = text.split('\\n')
                                            .map(line => line.trim())
                                            .filter(line => line.length > 20 && (
                                                line.includes('财联社') ||
                                                line.includes('【') ||
                                                line.includes('】')
                                            ));

                                        for (const line of lines.slice(0, 10)) {
                                            if (line.length > 20) {
                                                results.push(line);
                                            }
                                        }

                                        if (results.length > 0) {
                                            break;
                                        }
                                    }
                                }

                                return results;
                            }
                        """)

                        if news_data:
                            self.logger.info(f"JavaScript搜索找到 {len(news_data)} 条新闻")
                            for i, news_text in enumerate(news_data[:self.max_news]):
                                # 进一步分割长文本，提取多条新闻
                                sub_news = self._split_combined_news(news_text)

                                for j, sub_text in enumerate(sub_news):
                                    if len(sub_text) > 20:
                                        # 提取新闻标题
                                        title = self._extract_news_title(sub_text)

                                        news_item = NewsItem(
                                            title=title,
                                            content=sub_text,
                                            url=f"https://www.cls.cn/telegraph/red#{i+1}_{j+1}",
                                            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                            source="财联社",
                                            category="加红",
                                            tags=["财联社", "加红", "重要新闻", "DOM提取"]
                                        )
                                        news_list.append(news_item)

                    self.logger.info(f"最终从DOM提取到 {len(news_list)} 条加红新闻")
                    return news_list

                finally:
                    await browser.close()

        except ImportError:
            self.logger.error("Playwright未安装，请运行: pip install playwright && playwright install chromium")
            return []
        except Exception as e:
            self.logger.error(f"无头浏览器DOM提取失败: {e}")
            return []

    async def _handle_expand_buttons(self, page, container_element):
        """处理新闻中的展开按钮，点击展开查看全文"""
        try:
            # 在容器元素内查找可能的展开按钮
            expand_selectors = [
                'button:has-text("展开")',
                'span:has-text("展开")',
                'a:has-text("展开")',
                'div:has-text("展开")',
                'button:has-text("查看全文")',
                'span:has-text("查看全文")',
                'a:has-text("查看全文")',
                'button:has-text("更多")',
                'span:has-text("更多")',
                'a:has-text("更多")',
                '[class*="expand"]',
                '[class*="more"]',
                '[class*="unfold"]'
            ]

            expand_buttons_found = 0
            max_expand_buttons = 5  # 限制最多展开5个按钮

            for selector in expand_selectors:
                try:
                    # 在容器内查找展开按钮
                    expand_buttons = await container_element.query_selector_all(selector)

                    for button in expand_buttons:
                        # 检查是否已达到展开限制
                        if expand_buttons_found >= max_expand_buttons:
                            self.logger.info(f"已达到展开按钮限制 ({max_expand_buttons} 个)，停止展开")
                            break

                        try:
                            # 检查按钮是否可见和可点击
                            if await button.is_visible() and await button.is_enabled():
                                # 获取按钮文本确认是展开按钮
                                button_text = await button.text_content()
                                if button_text and any(keyword in button_text for keyword in ['展开', '查看全文', '更多', '全文']):
                                    self.logger.info(f"找到展开按钮 ({expand_buttons_found + 1}/{max_expand_buttons}): {button_text.strip()}")

                                    # 点击展开按钮
                                    await button.click()
                                    expand_buttons_found += 1

                                    # 等待内容加载
                                    await asyncio.sleep(1)

                                    self.logger.info(f"已点击展开按钮，等待内容加载...")
                        except Exception as e:
                            self.logger.debug(f"点击展开按钮失败: {e}")
                            continue

                    # 如果已达到限制，跳出外层循环
                    if expand_buttons_found >= max_expand_buttons:
                        break

                except Exception as e:
                    self.logger.debug(f"查找展开按钮失败 ({selector}): {e}")
                    continue

            if expand_buttons_found > 0:
                self.logger.info(f"总共点击了 {expand_buttons_found} 个展开按钮")
                # 额外等待时间确保所有内容都加载完成
                await asyncio.sleep(2)

        except Exception as e:
            self.logger.debug(f"处理展开按钮时出错: {e}")

    async def _handle_expand_buttons_for_red_news(self, page, container_element):
        """专门处理加红新闻的展开按钮，更精准和有限制"""
        try:
            # 专门针对加红新闻的展开按钮选择器
            red_news_expand_selectors = [
                # 财联社加红新闻特有的展开按钮
                '.red-news button:has-text("展开")',
                '.important-news button:has-text("展开")',
                '.highlight-news button:has-text("展开")',
                # 通用展开按钮，但限制在加红新闻容器内
                'button:has-text("展开")',
                'span:has-text("展开")',
                'a:has-text("展开")',
                'button:has-text("查看全文")',
                'span:has-text("查看全文")',
                # 带有特定类名的展开按钮
                '[class*="expand"][class*="red"]',
                '[class*="more"][class*="important"]'
            ]

            expand_buttons_found = 0
            max_expand_buttons = 3  # 对加红新闻限制为最多3个展开按钮

            self.logger.info(f"开始处理加红新闻展开按钮，最多展开 {max_expand_buttons} 个")

            for selector in red_news_expand_selectors:
                try:
                    # 在加红新闻容器内查找展开按钮
                    expand_buttons = await container_element.query_selector_all(selector)

                    for button in expand_buttons:
                        # 检查是否已达到展开限制
                        if expand_buttons_found >= max_expand_buttons:
                            self.logger.info(f"已达到加红新闻展开按钮限制 ({max_expand_buttons} 个)，停止展开")
                            return

                        try:
                            # 检查按钮是否可见和可点击
                            if await button.is_visible() and await button.is_enabled():
                                # 获取按钮文本确认是展开按钮
                                button_text = await button.text_content()
                                if button_text and any(keyword in button_text for keyword in ['展开', '查看全文', '更多', '全文']):
                                    self.logger.info(f"找到加红新闻展开按钮 ({expand_buttons_found + 1}/{max_expand_buttons}): {button_text.strip()}")

                                    # 点击展开按钮
                                    await button.click()
                                    expand_buttons_found += 1

                                    # 等待内容加载
                                    await asyncio.sleep(1.5)  # 稍微增加等待时间确保内容完全加载

                                    self.logger.info(f"已点击加红新闻展开按钮，等待内容加载...")
                        except Exception as e:
                            self.logger.debug(f"点击加红新闻展开按钮失败: {e}")
                            continue

                    # 如果已达到限制，跳出外层循环
                    if expand_buttons_found >= max_expand_buttons:
                        break

                except Exception as e:
                    self.logger.debug(f"查找加红新闻展开按钮失败 ({selector}): {e}")
                    continue

            if expand_buttons_found > 0:
                self.logger.info(f"总共点击了 {expand_buttons_found} 个加红新闻展开按钮")
                # 额外等待时间确保所有内容都加载完成
                await asyncio.sleep(2)
            else:
                self.logger.info("未找到加红新闻展开按钮")

        except Exception as e:
            self.logger.debug(f"处理加红新闻展开按钮时出错: {e}")

    async def _extract_clean_news_content(self, element):
        """从DOM元素中提取干净的新闻内容，排除统计信息"""
        try:
            # 使用JavaScript在浏览器中过滤掉统计信息元素
            clean_content = await element.evaluate("""
                (element) => {
                    // 克隆元素以避免修改原始DOM
                    const clonedElement = element.cloneNode(true);

                    // 定义需要移除的元素选择器
                    const selectorsToRemove = [
                        // 统计信息相关的选择器
                        '[class*="stat"]',
                        '[class*="count"]',
                        '[class*="view"]',
                        '[class*="read"]',
                        '[class*="comment"]',
                        '[class*="share"]',
                        '[class*="like"]',
                        '[class*="follow"]',
                        '[class*="meta"]',
                        '[class*="footer"]',
                        '[class*="action"]',
                        '[class*="toolbar"]',
                        '[class*="social"]',
                        // 具体的统计信息元素
                        '.view-count',
                        '.read-count',
                        '.comment-count',
                        '.share-count',
                        '.like-count',
                        '.stats',
                        '.metadata',
                        '.article-footer',
                        '.article-meta',
                        '.news-footer',
                        '.news-meta',
                        '.interaction',
                        '.engagement'
                    ];

                    // 移除匹配的元素
                    selectorsToRemove.forEach(selector => {
                        const elementsToRemove = clonedElement.querySelectorAll(selector);
                        elementsToRemove.forEach(el => el.remove());
                    });

                    // 移除包含统计信息文本的元素
                    const allElements = clonedElement.querySelectorAll('*');
                    allElements.forEach(el => {
                        const text = el.textContent || '';
                        // 检查是否包含统计信息模式
                        if (text.match(/阅\\d+\\.?\\d*[万千]?/) ||
                            text.match(/评论\\(\\d+\\)/) ||
                            text.match(/分享\\(\\d+\\)/) ||
                            text.match(/点赞\\(\\d+\\)/) ||
                            text.match(/\\d+\\.?\\d*[万千]?次阅读/) ||
                            text.match(/环球市场情报/) ||
                            text.includes('阅读量') ||
                            text.includes('浏览量') ||
                            text.includes('查看') && text.match(/\\d+/)) {
                            // 如果元素只包含统计信息，移除整个元素
                            if (el.textContent.trim().length < 50) {
                                el.remove();
                            } else {
                                // 如果元素包含其他内容，只移除统计信息部分
                                el.textContent = el.textContent
                                    .replace(/阅\\d+\\.?\\d*[万千]?/g, '')
                                    .replace(/评论\\(\\d+\\)/g, '')
                                    .replace(/分享\\(\\d+\\)/g, '')
                                    .replace(/点赞\\(\\d+\\)/g, '')
                                    .replace(/\\d+\\.?\\d*[万千]?次阅读/g, '')
                                    .replace(/环球市场情报/g, '')
                                    .replace(/阅读量\\d+\\.?\\d*[万千]?/g, '')
                                    .replace(/浏览量\\d+\\.?\\d*[万千]?/g, '');
                            }
                        }
                    });

                    return clonedElement.textContent || '';
                }
            """)

            return clean_content.strip() if clean_content else ""

        except Exception as e:
            self.logger.debug(f"提取干净新闻内容失败，使用原始方法: {e}")
            # 如果JavaScript方法失败，回退到原始方法
            try:
                return await element.text_content() or ""
            except:
                return ""

    def _extract_news_title(self, content: str) -> str:
        """从新闻内容中提取标题"""
        try:
            # 按行分割内容
            lines = content.strip().split('\n')

            # 找到第一行非空内容作为标题
            for line in lines:
                line = line.strip()
                if line and len(line) > 10:  # 标题应该有一定长度
                    # 限制标题长度
                    if len(line) > 100:
                        return line[:100] + "..."
                    return line

            # 如果没有找到合适的标题，使用前100个字符
            if len(content) > 100:
                return content[:100] + "..."
            return content

        except Exception as e:
            self.logger.debug(f"提取新闻标题失败: {e}")
            return content[:50] + "..." if len(content) > 50 else content

    def _clean_news_content(self, content: str) -> str:
        """清理新闻内容，去除无意义的信息"""
        if not content:
            return content

        import re

        # 去除阅读量、评论数、分享数等信息
        # 匹配模式：阅XX万、评论(XX)、分享(XX)等
        patterns_to_remove = [
            r'阅\d+\.?\d*[万千]?',  # 阅34.95W、阅1000等
            r'评论\(\d+\)',        # 评论(0)、评论(123)等
            r'分享\(\d+\)',        # 分享(22)、分享(0)等
            r'点赞\(\d+\)',        # 点赞(10)等
            r'转发\(\d+\)',        # 转发(5)等
            r'收藏\(\d+\)',        # 收藏(8)等
            r'\d+\.?\d*[万千]?次阅读',  # 34.95万次阅读等
            r'\d+\.?\d*[万千]?人阅读',  # 1000人阅读等
            r'阅读量\d+\.?\d*[万千]?',  # 阅读量34.95万等
            r'浏览量\d+\.?\d*[万千]?',  # 浏览量1000等
            r'查看\d+\.?\d*[万千]?',    # 查看34.95万等
            r'\d+\.?\d*[万千]?次查看',  # 34.95万次查看等
            r'环球市场情报',           # 环球市场情报
            r'财联社\d{1,2}月\d{1,2}日讯',  # 财联社7月10日讯
            r'来源：[^，。\n]*',       # 来源：XXX
            r'编辑：[^，。\n]*',       # 编辑：XXX
            r'责编：[^，。\n]*',       # 责编：XXX
            r'记者：[^，。\n]*',       # 记者：XXX
        ]

        cleaned_content = content

        # 逐个应用清理模式
        for pattern in patterns_to_remove:
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.IGNORECASE)

        # 去除多余的空白字符
        cleaned_content = re.sub(r'\s+', ' ', cleaned_content)  # 多个空格合并为一个
        cleaned_content = re.sub(r'\n\s*\n', '\n', cleaned_content)  # 多个换行合并
        cleaned_content = cleaned_content.strip()  # 去除首尾空白

        # 去除末尾的标点符号组合（如：。。。、！！！等）
        cleaned_content = re.sub(r'[。！？，、；：\s]+$', '', cleaned_content)

        return cleaned_content

    def _extract_individual_news(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取独立的新闻条目"""
        news_items = []

        # 使用多种分割方式识别独立新闻
        # 方法1: 按时间戳分割（如 08:56:15【...】）
        import re
        time_news_pattern = r'(\d{2}:\d{2}:\d{2}【[^】]+】[^】]*?(?=\d{2}:\d{2}:\d{2}【|$))'
        time_matches = re.findall(time_news_pattern, text, re.DOTALL)

        for match in time_matches:
            cleaned_match = self._clean_news_content(match)
            title = self._extract_news_title(cleaned_match)
            if len(cleaned_match) > 100:  # 确保内容足够长
                news_items.append({
                    'title': title,
                    'content': cleaned_match
                })

        # 方法2: 按【】标题分割
        if not news_items:
            bracket_pattern = r'【[^】]+】[^【]*?(?=【|$)'
            bracket_matches = re.findall(bracket_pattern, text, re.DOTALL)

            for match in bracket_matches:
                cleaned_match = self._clean_news_content(match)
                if len(cleaned_match) > 50:  # 确保内容足够长
                    title = self._extract_news_title(cleaned_match)
                    news_items.append({
                        'title': title,
                        'content': cleaned_match
                    })

        # 方法3: 如果还是没有，按段落分割
        if not news_items:
            paragraphs = text.split('\n\n')
            for paragraph in paragraphs:
                cleaned_paragraph = self._clean_news_content(paragraph.strip())
                if len(cleaned_paragraph) > 100 and ('【' in cleaned_paragraph or '财联社' in cleaned_paragraph):
                    title = self._extract_news_title(cleaned_paragraph)
                    news_items.append({
                        'title': title,
                        'content': cleaned_paragraph
                    })

        # 如果所有方法都失败，返回整个文本作为一条新闻
        if not news_items:
            cleaned_text = self._clean_news_content(text)
            title = self._extract_news_title(cleaned_text)
            news_items.append({
                'title': title,
                'content': cleaned_text
            })

        # 限制返回数量并去重
        unique_items = []
        seen_titles = set()

        for item in news_items:
            if item['title'] not in seen_titles and len(item['content']) > 50:
                unique_items.append(item)
                seen_titles.add(item['title'])

                # 限制最多返回10条新闻
                if len(unique_items) >= 10:
                    break

        return unique_items

    def _is_red_category_content(self, text: str) -> bool:
        """判断内容是否为加红栏目内容"""
        # 检查文本是否包含加红新闻的特征
        red_indicators = [
            '【',  # 财联社新闻标题格式
            '】',
            '财联社',
            '重要',
            '紧急',
            '突发',
            '快讯'
        ]

        # 至少包含2个指标才认为是加红内容
        indicator_count = sum(1 for indicator in red_indicators if indicator in text)
        return indicator_count >= 2

    def _extract_news_lines(self, text: str) -> List[str]:
        """从文本中提取新闻行"""
        lines = []

        # 按行分割
        raw_lines = text.split('\n')

        for line in raw_lines:
            line = line.strip()

            # 过滤条件：
            # 1. 长度大于20字符
            # 2. 包含财联社新闻特征
            # 3. 不是纯数字或时间
            if (len(line) > 20 and
                ('【' in line or '财联社' in line or '：' in line) and
                not line.isdigit() and
                not self._is_pure_time(line)):

                # 清理文本
                cleaned_line = self._clean_news_text(line)
                if cleaned_line:
                    lines.append(cleaned_line)

        return lines

    def _is_pure_time(self, text: str) -> bool:
        """判断是否为纯时间文本"""
        import re
        time_patterns = [
            r'^\d{2}:\d{2}:\d{2}$',  # HH:MM:SS
            r'^\d{4}-\d{2}-\d{2}$',  # YYYY-MM-DD
            r'^\d{1,2}月\d{1,2}日$',  # X月X日
        ]

        for pattern in time_patterns:
            if re.match(pattern, text.strip()):
                return True
        return False

    def _clean_news_text(self, text: str) -> str:
        """清理新闻文本"""
        # 移除多余的空白字符
        text = ' '.join(text.split())

        # 移除一些无用的前缀
        prefixes_to_remove = [
            '点击查看',
            '更多详情',
            '相关阅读',
            '推荐阅读'
        ]

        for prefix in prefixes_to_remove:
            if text.startswith(prefix):
                text = text[len(prefix):].strip()

        return text if len(text) > 10 else None

    def _split_combined_news(self, text: str) -> List[str]:
        """分割合并的新闻文本"""
        import re

        # 按财联社新闻格式分割
        # 格式通常是：【标题】内容
        news_items = []

        # 方法1: 按【】分割
        parts = re.split(r'【([^】]+)】', text)

        current_news = ""
        for i, part in enumerate(parts):
            if i % 2 == 1:  # 奇数索引是标题
                if current_news:
                    news_items.append(current_news.strip())
                current_news = f"【{part}】"
            else:  # 偶数索引是内容
                if part.strip():
                    current_news += part.strip()

        if current_news:
            news_items.append(current_news.strip())

        # 方法2: 如果没有【】格式，按时间戳分割
        if not news_items:
            time_pattern = r'\d{2}:\d{2}:\d{2}'
            time_splits = re.split(time_pattern, text)

            for split in time_splits:
                if len(split.strip()) > 50:
                    news_items.append(split.strip())

        # 方法3: 如果还是没有，按句号分割长文本
        if not news_items and len(text) > 200:
            sentences = text.split('。')
            current_item = ""

            for sentence in sentences:
                current_item += sentence + "。"
                if len(current_item) > 100:
                    news_items.append(current_item.strip())
                    current_item = ""

            if current_item:
                news_items.append(current_item.strip())

        # 如果所有方法都失败，返回原文本
        if not news_items:
            news_items = [text]

        return news_items[:self.max_news]  # 限制数量

    def _extract_news_title(self, text: str) -> str:
        """提取新闻标题"""
        import re

        # 方法1: 提取【】中的内容作为标题
        title_match = re.search(r'【([^】]+)】', text)
        if title_match:
            return title_match.group(1)

        # 方法2: 提取第一句话作为标题
        sentences = text.split('。')
        if sentences and len(sentences[0]) > 10:
            title = sentences[0].strip()
            # 移除时间戳
            title = re.sub(r'\d{2}:\d{2}:\d{2}', '', title).strip()
            if len(title) > 10:
                return title

        # 方法3: 取前100个字符作为标题
        title = text[:100].strip()
        if '。' in title:
            title = title.split('。')[0]

        return title if title else text[:50] + "..."
    
    async def _fetch_api_news_with_signature(self, session, signature_data: Dict[str, str]) -> List[NewsItem]:
        """使用签名调用API获取新闻"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': 'https://www.cls.cn/telegraph',
                'Origin': 'https://www.cls.cn',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            self.logger.info(f"使用签名调用API: {self.api_url}")
            
            async with session.get(self.api_url, params=signature_data, headers=headers, timeout=self.timeout) as response:
                self.logger.info(f"API响应状态码: {response.status}")
                
                if response.status != 200:
                    self.logger.error(f"API请求失败: {response.status}")
                    return []
                
                json_data = await response.json()
                errno = json_data.get('errno', 'unknown')
                msg = json_data.get('msg', 'unknown')
                
                self.logger.info(f"API响应: errno={errno}, msg={msg}")
                
                if errno == 0 or errno == '0':
                    return self._parse_api_response(json_data)
                else:
                    self.logger.error(f"API返回错误: {msg}")
                    return []
                    
        except Exception as e:
            self.logger.error(f"API调用失败: {e}")
            return []
    
    def _parse_api_response(self, json_data) -> List[NewsItem]:
        """解析API响应"""
        news_list = []
        
        try:
            data = json_data.get('data', {})
            roll_data = data.get('roll_data', [])
            
            self.logger.info(f"API返回 {len(roll_data)} 条新闻数据")
            
            for i, item in enumerate(roll_data[:self.max_news]):
                if not isinstance(item, dict):
                    continue
                
                # 提取新闻字段
                title = item.get('title', '') or item.get('brief', '')
                content = item.get('content', '') or item.get('brief', '') or title
                
                # 时间戳转换
                ctime = item.get('ctime', 0)
                if ctime:
                    try:
                        if len(str(ctime)) == 10:  # 秒级时间戳
                            timestamp = datetime.fromtimestamp(ctime).strftime('%Y-%m-%d %H:%M:%S')
                        else:  # 毫秒级时间戳
                            timestamp = datetime.fromtimestamp(ctime/1000).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                else:
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 构建新闻URL
                news_id = item.get('id', '') or item.get('news_id', '')
                if news_id:
                    url = f"https://www.cls.cn/detail/{news_id}"
                else:
                    url = f"https://www.cls.cn/telegraph#{i+1}"
                
                # 验证和创建新闻项
                if title and len(title.strip()) > 5:
                    news_item = NewsItem(
                        title=self.clean_text(title),
                        content=self.clean_text(content) or f"财联社消息，{title}。",
                        url=url,
                        timestamp=timestamp,
                        source="财联社",
                        category="财经",
                        tags=["财联社", "财经新闻", "无头浏览器"]
                    )
                    news_list.append(news_item)
                    self.logger.debug(f"成功解析新闻: {title[:50]}...")
            
            self.logger.info(f"财联社无头浏览器解析获取到 {len(news_list)} 条有效新闻")
            
        except Exception as e:
            self.logger.error(f"API响应解析失败: {e}")
        
        return news_list
