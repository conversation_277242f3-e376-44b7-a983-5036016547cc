#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单邮件发送器 - 绕过队列系统的复杂性
"""

import smtplib
import asyncio
import sqlite3
import hashlib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from pathlib import Path
import logging

class SimpleEmailSender:
    """简单邮件发送器 - 使用独立函数方式"""

    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('simple_email')
        self.db_path = Path(__file__).parent / "email_sent.db"

        # 硬编码配置，避免任何配置问题
        self.username = "<EMAIL>"
        self.password = "czoexruvumdubgdf"
        self.to_email = "<EMAIL>"
        self.enabled = True

        # 初始化推送记录数据库
        self.init_sent_database()

    def init_sent_database(self):
        """初始化推送记录数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sent_emails (
                    id INTEGER PRIMARY KEY,
                    news_hash TEXT UNIQUE,
                    news_title TEXT,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()

    def _get_news_hash(self, news):
        """生成新闻哈希值（仅基于标题，用于去重）"""
        title = news.get('title', '').strip()
        # 清理标题中的特殊字符和多余空格，确保一致性
        title = ' '.join(title.split())
        return hashlib.md5(title.encode('utf-8')).hexdigest()

    def _is_already_sent(self, news_hash):
        """检查是否已经推送过"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT COUNT(*) FROM sent_emails WHERE news_hash = ?',
                    (news_hash,)
                )
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            self.logger.error(f"检查推送记录失败: {e}")
            return False

    def _mark_as_sent(self, news_hash, news_title):
        """标记为已推送"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO sent_emails (news_hash, news_title)
                    VALUES (?, ?)
                ''', (news_hash, news_title))
                conn.commit()
        except Exception as e:
            self.logger.error(f"标记推送记录失败: {e}")

    async def send_analysis_report(self, analysis_results):
        """发送分析报告 - 带去重功能"""
        if not self.enabled:
            self.logger.info("邮件推送已禁用")
            return

        if not analysis_results:
            self.logger.info("没有分析结果需要推送")
            return

        # 过滤已推送的新闻
        filtered_results = []
        skipped_count = 0

        for result in analysis_results:
            news = result.get('news', {})
            news_hash = self._get_news_hash(news)

            if self._is_already_sent(news_hash):
                skipped_count += 1
                self.logger.debug(f"跳过已推送的新闻: {news.get('title', '')[:50]}...")
            else:
                filtered_results.append(result)

        if skipped_count > 0:
            self.logger.info(f"跳过 {skipped_count} 条已推送的新闻")
            print(f"跳过 {skipped_count} 条已推送的新闻")

        if not filtered_results:
            self.logger.info("所有新闻都已推送过，无需重复推送")
            print("所有新闻都已推送过，无需重复推送")
            return

        self.logger.info(f"开始发送 {len(filtered_results)} 条新闻分析邮件")
        print(f"开始发送 {len(filtered_results)} 条新闻分析邮件")

        success_count = 0
        for i, result in enumerate(filtered_results, 1):
            try:
                news = result.get('news', {})
                news_hash = self._get_news_hash(news)

                # 生成邮件内容
                subject = news.get('title', '财经新闻分析')
                html_content = self._generate_email_content(result, i, len(filtered_results))

                # 发送邮件
                success = await self._send_single_email(subject, html_content)

                if success:
                    success_count += 1
                    # 标记为已推送
                    self._mark_as_sent(news_hash, subject)
                    self.logger.info(f"第 {i}/{len(filtered_results)} 条邮件发送成功: {subject}")
                    print(f"第 {i}/{len(filtered_results)} 条邮件发送成功: {subject}")
                else:
                    self.logger.error(f"第 {i}/{len(filtered_results)} 条邮件发送失败: {subject}")
                    print(f"第 {i}/{len(filtered_results)} 条邮件发送失败: {subject}")

                # 邮件间延迟
                if i < len(filtered_results):
                    await asyncio.sleep(3)

            except Exception as e:
                self.logger.error(f"发送第 {i} 条邮件时出错: {e}")

        self.logger.info(f"邮件发送完成: 成功 {success_count}/{len(filtered_results)} 条")
        print(f"邮件发送完成: 成功 {success_count}/{len(filtered_results)} 条")

    async def _send_single_email(self, subject, html_content):
        """发送单封邮件 - 使用超简单方法"""
        try:
            # 使用与成功测试完全相同的逻辑
            msg = MIMEText(html_content, 'html', 'utf-8')
            msg['From'] = self.username
            msg['To'] = self.to_email
            msg['Subject'] = subject

            # 直接使用成功的发送逻辑
            server = smtplib.SMTP_SSL('smtp.qq.com', 465, timeout=30)
            server.login(self.username, self.password)
            server.send_message(msg)
            server.quit()

            return True

        except Exception as e:
            self.logger.error(f"邮件发送失败: {e}")
            return False

    def _generate_email_content(self, result, current_index, total_count):
        """生成邮件内容"""
        news = result.get('news', {})
        analysis = result.get('analysis', {})
        
        # 获取分析详细信息
        markdown_report = analysis.get('markdown_report', '')
        model_used = analysis.get('model_used', 'unknown')
        thinking_time = analysis.get('thinking_time', 0)
        thinking_tokens = analysis.get('thinking_tokens', 0)

        # 简单的Markdown到HTML转换
        html_report = self._markdown_to_html(markdown_report)

        html_content = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <title>{news.get('title', '财经新闻分析')}</title>
            <style>
                body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; }}
                .header {{ background: #3498db; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .news-content {{ background: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 15px 0; }}
                .analysis {{ background: #fff; padding: 20px; border-left: 4px solid #e74c3c; margin: 15px 0; }}
                .footer {{ background: #2c3e50; color: white; padding: 15px; text-align: center; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{news.get('title', '财经新闻分析')}</h1>
                <p>AI新闻分析系统 - 第 {current_index}/{total_count} 条</p>
            </div>
            
            <div class="content">
                <div class="news-content">
                    <h3>新闻内容</h3>
                    <p>{news.get('content', '新闻内容获取失败')}</p>
                    <p><strong>来源:</strong> {news.get('source', '未知')}</p>
                    <p><strong>时间:</strong> {news.get('timestamp', '未知')}</p>
                </div>
                
                <div class="analysis">
                    <h3>AI分析报告</h3>
                    <div>{html_report}</div>
                    <p><strong>分析模型:</strong> {model_used}</p>
                    {f'<p><strong>思考时间:</strong> {thinking_time:.1f}秒</p>' if thinking_time > 0 else ''}
                    {f'<p><strong>思考Tokens:</strong> {thinking_tokens:,}</p>' if thinking_tokens > 0 else ''}
                </div>
            </div>
            
            <div class="footer">
                <p>AI新闻分析系统 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
        
        return html_content

    def _markdown_to_html(self, markdown_text):
        """简单的Markdown到HTML转换"""
        if not markdown_text:
            return ""
        
        import re
        html = markdown_text
        
        # 转换标题
        html = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
        html = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
        html = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
        
        # 转换粗体
        html = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html)
        
        # 转换段落
        paragraphs = html.split('\n\n')
        html_paragraphs = []
        for p in paragraphs:
            p = p.strip()
            if p and not p.startswith('<'):
                html_paragraphs.append(f'<p>{p}</p>')
            else:
                html_paragraphs.append(p)
        
        return '\n'.join(html_paragraphs)
