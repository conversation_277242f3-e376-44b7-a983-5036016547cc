@echo off
chcp 65001 >nul
title AI新闻分析系统 - 快速启动

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🚀 AI新闻分析系统 - 快速启动                                               ║
echo ║                                                                              ║
echo ║    💡 这将直接启动持久化运行模式                                              ║
echo ║    🔄 系统每30分钟自动运行一次                                                ║
echo ║    🌙 夜间23:00-7:00自动暂停                                                 ║
echo ║    🛑 按 Ctrl+C 停止系统                                                     ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

echo 🔍 检查配置文件...
if not exist "config.json" (
    echo ❌ 错误: 未找到config.json配置文件
    echo 💡 请先配置API密钥和邮箱设置
    pause
    exit /b 1
)

echo ✅ 配置文件存在
echo.

echo 🚀 启动AI新闻分析系统...
echo 💡 系统正在初始化，请稍候...
echo.

title AI新闻分析系统 - 正在运行

REM 启动持久化运行
python main_persistent.py

echo.
echo 🛑 系统已停止运行
title AI新闻分析系统 - 已停止

echo.
echo 📊 运行结果:
echo   - 如果是正常停止: 系统已安全关闭
echo   - 如果是异常停止: 请检查日志文件 news_analysis.log
echo.

pause
