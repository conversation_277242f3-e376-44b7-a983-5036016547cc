#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析完整性修复效果
"""

import asyncio
import json
from config import load_config
from analyzer import NewsAnalyzer

async def test_analysis_completeness():
    """测试分析完整性"""
    print("🧪 测试分析完整性修复效果...")
    print("=" * 60)
    
    # 使用您收到的那条新闻进行测试
    test_news = {
        'title': '特朗普：下周一将发布有关俄罗斯的"重大声明"',
        'content': '财联社7月11日电，据央视新闻，美国总统特朗普7月10日透露，将于下周一（当地时间7月14日）就俄罗斯问题发表"重大声明"。特朗普在当天接受美国媒体电话采访时表示，当前俄军空袭乌克兰加剧，特朗普本人对俄罗斯总统普京表示"失望"，并称普京"口头客气但毫无实质"。',
        'source': '财联社',
        'timestamp': '2025-07-11 11:27:08',
        'url': 'http://test.com/news/trump-russia'
    }
    
    try:
        # 加载配置
        config = load_config()
        
        # 显示当前配置
        print("📋 当前配置:")
        print(f"   主要模型: {config['analyzer']['primary_provider']}")
        print(f"   备用模型: {config['analyzer']['fallback_provider']}")
        print(f"   Gemini max_tokens: {config['analyzer']['gemini']['max_tokens']}")
        print(f"   DeepSeek max_tokens: {config['analyzer']['deepseek']['max_tokens']}")
        print(f"   超时时间: {config['analyzer']['timeout']}秒")
        print(f"   允许mock: {config['analyzer']['allow_mock_analysis']}")
        print()
        
        # 创建分析器
        analyzer = NewsAnalyzer(config)
        
        print(f"🔬 开始分析新闻: {test_news['title']}")
        print("⏱️ 这可能需要1-2分钟，请耐心等待...")
        print()
        
        # 分析新闻
        result = await analyzer.analyze_news(test_news)
        
        if result:
            model_used = result.get('model_used', 'unknown')
            markdown_report = result.get('markdown_report', '')
            
            print("✅ 分析完成！")
            print(f"🤖 使用模型: {model_used}")
            print(f"📝 报告长度: {len(markdown_report)} 字符")
            print()
            
            # 检查分析完整性
            print("🔍 分析完整性检查:")
            
            # 检查长度
            if len(markdown_report) >= 500:
                print(f"   ✅ 长度检查: {len(markdown_report)} 字符 (≥500)")
            else:
                print(f"   ❌ 长度检查: {len(markdown_report)} 字符 (<500)")
            
            # 检查关键要素
            required_elements = ["好公司", "好行业", "好价格", "好时机"]
            found_elements = [elem for elem in required_elements if elem in markdown_report]
            missing_elements = [elem for elem in required_elements if elem not in markdown_report]
            
            print(f"   ✅ 包含要素: {found_elements}")
            if missing_elements:
                print(f"   ⚠️ 缺少要素: {missing_elements}")
            else:
                print(f"   ✅ 四好框架完整")
            
            # 检查是否包含评分
            has_rating = any(star in markdown_report for star in ["⭐", "★", "星", "分"])
            print(f"   {'✅' if has_rating else '⚠️'} 评分系统: {'包含' if has_rating else '缺少'}")
            
            # 检查结构完整性
            sections = ["解构分析", "框架分析", "综合判断", "跟踪要点"]
            found_sections = [sec for sec in sections if sec in markdown_report]
            print(f"   ✅ 结构完整性: {len(found_sections)}/4 个主要部分")
            
            print()
            print("📄 分析报告预览 (前500字符):")
            print("-" * 50)
            print(markdown_report[:500])
            if len(markdown_report) > 500:
                print("...")
                print(f"(还有 {len(markdown_report) - 500} 个字符)")
            print("-" * 50)
            
            # 总体评估
            print()
            print("📊 总体评估:")
            if len(markdown_report) >= 500 and len(missing_elements) <= 2:
                print("   🎉 分析质量: 优秀 - 完整且详细")
                return True
            elif len(markdown_report) >= 300:
                print("   ✅ 分析质量: 良好 - 基本完整")
                return True
            else:
                print("   ⚠️ 分析质量: 需要改进 - 可能不完整")
                return False
        else:
            print("❌ 分析失败，返回空结果")
            return False
            
    except Exception as e:
        error_str = str(e)
        print(f"❌ 分析过程中出现异常: {error_str}")
        
        if "过短" in error_str or "截断" in error_str:
            print("💡 这是预期的错误，说明完整性检查正在工作")
            print("🔧 建议检查API配置和网络连接")
        elif "缺少关键要素" in error_str:
            print("💡 分析结果不符合四好框架要求")
            print("🔧 建议检查提示词配置")
        elif "所有AI模型都失败" in error_str:
            print("💡 所有API都无法访问")
            print("🔧 建议检查网络连接和API密钥")
        
        return False

async def main():
    """主函数"""
    print("🔧 分析完整性测试工具")
    print("=" * 60)
    print("📝 此工具用于测试修复后的分析完整性检查功能")
    print()
    
    success = await test_analysis_completeness()
    
    print()
    print("📋 测试总结:")
    print("=" * 30)
    if success:
        print("🎉 测试成功！分析功能正常工作")
        print("📧 现在应该不会再收到不完整的分析邮件")
    else:
        print("⚠️ 测试未完全通过，请检查配置")
        print("💡 建议:")
        print("   1. 检查API密钥是否正确")
        print("   2. 检查网络连接是否正常")
        print("   3. 检查代理设置是否正确")
        print("   4. 查看日志文件了解详细错误")
    
    print()
    print("🔧 修复内容回顾:")
    print("   ✅ DeepSeek max_tokens: 1000 → 4000")
    print("   ✅ 分析超时时间: 60秒 → 120秒")
    print("   ✅ 增加了分析完整性检查")
    print("   ✅ 增加了四好框架要素验证")
    print("   ✅ 禁用了模拟分析")

if __name__ == "__main__":
    asyncio.run(main())
