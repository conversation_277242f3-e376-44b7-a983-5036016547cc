@echo off
chcp 65001 >nul

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🛑 AI新闻分析系统 - 停止后台运行                                           ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 查找后台运行的新闻分析进程...

REM 查找包含main_persistent.py的Python进程
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| findstr /i "python.exe"') do (
    set "pid=%%i"
    set "pid=!pid:"=!"
    
    REM 检查进程命令行是否包含main_persistent.py
    wmic process where "processid=!pid!" get commandline /format:list 2>nul | findstr /i "main_persistent.py" >nul
    if not errorlevel 1 (
        echo 🎯 找到新闻分析进程: PID !pid!
        echo 🛑 正在停止进程...
        taskkill /pid !pid! /f >nul 2>&1
        if not errorlevel 1 (
            echo ✅ 进程已成功停止
            set "found=1"
        ) else (
            echo ❌ 停止进程失败
        )
    )
)

if not defined found (
    echo 💡 未找到运行中的新闻分析系统后台进程
    echo 📋 可能的情况:
    echo   - 系统未在后台运行
    echo   - 系统已经停止
    echo   - 进程名称不匹配
)

echo.
echo 🔍 再次检查是否还有相关进程...
tasklist /fi "imagename eq python.exe" | findstr /i "python.exe" >nul
if not errorlevel 1 (
    echo 📋 当前仍有Python进程运行:
    tasklist /fi "imagename eq python.exe"
    echo.
    echo 💡 如果上述进程中仍有新闻分析系统，请手动结束
) else (
    echo ✅ 没有发现Python进程运行
)

echo.
echo 📊 操作完成
echo 💡 如需重新启动，请运行 "后台运行.bat"
echo.

pause
