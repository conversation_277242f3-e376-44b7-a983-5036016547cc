# 新闻分析系统 - 最简版本

## 🎯 项目简介

这是一个专业的财经新闻分析系统，基于AI技术对财联社新闻进行深度分析。系统采用模块化设计，支持多AI模型，具备完整的爬虫、分析、推送功能。

### 核心特性
- 🤖 **双AI模型支持**：Gemini 2.5 Pro + DeepSeek R1 双重保障
- 📰 **智能新闻爬取**：财联社实时新闻获取，支持去重和增量更新
- 📊 **专业投研分析**：基于四好框架的A股量化投研分析
- 📧 **邮件智能推送**：HTML格式报告，支持去重和批量发送
- 🔄 **持久化运行**：支持定时自动运行，智能调度和错误恢复
- 🚫 **纯AI分析**：完全禁用模拟分析，确保所有结果来自真实AI模型

持久化运行 命令：
python main_persistent.py

## 🏗️ 系统架构

```mermaid
graph TD
    A[财联社新闻源] --> B[新闻爬虫]
    B --> C[SQLite数据库]
    C --> D[AI分析引擎]
    D --> E[Gemini 2.5 Pro]
    D --> F[DeepSeek R1]
    E --> G[分析结果]
    F --> G
    G --> H[邮件推送]
    H --> I[HTML报告]
```

### 文件结构
```
新闻分析-最简版本/
├── main.py                    # 单次运行入口
├── main_persistent.py         # 持久化运行入口 ⭐
├── config.py                  # 配置管理
├── config.json                # 系统配置文件 ⭐
├── crawler.py                 # 新闻爬虫模块
├── analyzer.py                # AI分析模块
├── pusher.py                  # 邮件推送模块
├── simple_email_sender.py     # 邮件发送器
├── thinking_model_analyzer.py # 思考模型分析器
├── start_persistent.bat       # Windows启动脚本
├── start_persistent.py        # 跨平台启动脚本
├── requirements.txt           # Python依赖
├── news_sources/              # 新闻源模块
│   ├── cls_source.py         # 财联社API源
│   ├── cls_browser_source.py # 财联社浏览器源
│   └── multi_source_manager.py # 多源管理器
├── 四好框架财经新闻分析思维流程.md # 分析框架说明
└── README.md                  # 使用文档
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保Python 3.8+
python --version

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置系统
编辑 `config.json` 文件，配置API密钥和邮箱：

```json
{
  "analyzer": {
    "primary_provider": "gemini",
    "fallback_provider": "deepseek",
    "allow_mock_analysis": false,
    "gemini": {
      "api_key": "your_gemini_api_key",
      "enabled": true
    },
    "deepseek": {
      "api_key": "your_deepseek_api_key",
      "enabled": true
    }
  },
  "pusher": {
    "username": "<EMAIL>",
    "password": "your_email_app_password",
    "to_email": "<EMAIL>",
    "enabled": true
  },
  "scheduler": {
    "enabled": true,
    "interval_minutes": 30,
    "status_report_interval": 0
  }
}
```

### 3. 运行系统

#### 单次运行（测试用）
```bash
python main.py
```

#### 持久化运行（推荐）
```bash
# 方式1：直接运行
python main_persistent.py

# 方式2：使用启动脚本
python start_persistent.py

# 方式3：Windows批处理（Windows用户）
start_persistent.bat
```

## ⚙️ 详细配置说明

### 🤖 AI分析配置
```json
"analyzer": {
  "primary_provider": "gemini",        // 主要AI模型：gemini/kimi/deepseek
  "fallback_provider": "kimi",         // 备用AI模型
  "allow_mock_analysis": false,        // 禁用模拟分析（重要！）
  "batch_size": 5,                     // 批量分析大小
  "timeout": 60,                       // API调用超时时间
  "kimi": {
    "api_key": "your_key",             // Kimi API密钥
    "model": "kimi-k2-0711-preview",   // 使用Kimi K2模型
    "enabled": true,                   // 启用Kimi
    "max_tokens": 4000,                // 最大输出token数
    "temperature": 0.6                 // 温度参数
  },
  "gemini": {
    "api_key": "your_key",             // Gemini API密钥
    "model": "gemini-2.5-pro",         // 使用Gemini 2.5 Pro
    "enabled": true,                   // 启用Gemini
    "proxy_url": "http://127.0.0.1:7897" // 代理设置（如需要）
  },
  "deepseek": {
    "api_key": "your_key",             // DeepSeek API密钥
    "model": "deepseek-reasoner",      // 使用DeepSeek R1
    "enabled": true                    // 启用DeepSeek
  }
}
```

### 📰 爬虫配置
```json
"crawler": {
  "global_timeout": 60,                // 全局超时时间
  "max_concurrent": 3,                 // 最大并发数
  "total_max_news": 20                 // 单次最大新闻数
},
"news_sources": {
  "cls": {
    "enabled": true,                   // 启用财联社
    "max_news": 10,                    // 最大新闻数
    "timeout": 30                      // 请求超时
  }
}
```

### 📧 邮件推送配置
```json
"pusher": {
  "smtp_server": "smtp.qq.com",        // SMTP服务器
  "smtp_port": 587,                    // SMTP端口
  "username": "<EMAIL>",           // 发送邮箱
  "password": "app_password",          // 邮箱应用密码
  "to_email": "<EMAIL>",         // 接收邮箱
  "enabled": true                      // 启用邮件推送
}
```

### ⏰ 调度器配置
```json
"scheduler": {
  "enabled": true,                     // 启用定时运行
  "interval_minutes": 30,              // 运行间隔（分钟）
  "max_runs": 0,                       // 最大运行次数（0=无限）
  "status_report_interval": 0,         // 状态报告间隔（0=禁用）
  "quiet_hours": {
    "enabled": true,                   // 启用静默时间
    "start_hour": 23,                  // 静默开始时间
    "end_hour": 7                      // 静默结束时间
  }
}
```

## 🛠️ 核心功能特性

### 🤖 双AI模型架构
- **主要模型**：Gemini 2.5 Pro（强推理能力）
- **备用模型**：DeepSeek R1（高性价比）
- **智能切换**：主模型失败自动切换备用模型
- **纯AI保证**：完全禁用模拟分析，确保真实AI结果

### 📊 专业投研分析
- **四好框架**：好公司/好行业/好价格/好时机分析
- **5W1H解构**：Who/What/When/Where/Why/How全面分析
- **情感分析**：正面/中性/负面情感倾向判断
- **实体识别**：自动提取相关公司、行业、概念

### 🔄 智能调度系统
- **定时运行**：可配置运行间隔（默认30分钟）
- **静默时间**：夜间自动暂停（23:00-7:00）
- **错误恢复**：网络异常自动重试，API失败智能降级
- **状态监控**：可选的系统状态报告（已默认禁用）

### 📧 智能邮件推送
- **去重机制**：基于新闻标题哈希，避免重复推送
- **HTML报告**：专业格式的分析报告
- **批量发送**：支持多条新闻批量推送
- **发送记录**：SQLite数据库记录推送历史

### 🗄️ 数据管理
- **SQLite存储**：新闻数据、分析结果、推送记录
- **智能去重**：多维度去重机制
- **缓存优化**：内存缓存避免重复分析
- **数据清理**：自动清理过期数据

## 📧 邮件报告格式

系统生成的HTML邮件报告包含：

### 📰 新闻信息
- 新闻标题和完整内容
- 新闻来源和发布时间
- 新闻链接（如有）

### 🧠 AI分析结果
- **四好框架分析**：投资维度评分和建议
- **情感倾向**：正面/中性/负面判断
- **核心实体**：相关公司、行业、概念
- **投资建议**：基于分析的操作建议
- **风险提示**：潜在风险和注意事项

### 🤖 技术信息
- 使用的AI模型（Gemini/DeepSeek）
- 分析置信度评分
- 思考时间和Token消耗（如适用）

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 🚫 收到"分析模型: mock"邮件
**问题**：邮件显示使用了模拟分析而非真实AI
**解决**：
```bash
# 检查配置文件中的设置
"allow_mock_analysis": false  # 确保为false

# 检查API密钥是否正确配置
"gemini": {"api_key": "your_real_key", "enabled": true}
"deepseek": {"api_key": "your_real_key", "enabled": true}
```

#### 2. 📧 邮件发送失败
**问题**：无法发送邮件或发送失败
**解决**：
- 检查SMTP配置是否正确
- 使用邮箱**应用专用密码**而非登录密码
- 确认邮箱已开启SMTP服务
- 检查网络连接和防火墙设置

#### 3. 🤖 API调用失败
**问题**：AI分析失败，所有模型都无法使用
**解决**：
- 验证API密钥的有效性
- 检查API配额是否充足
- 确认网络连接（可能需要代理）
- 查看日志文件了解具体错误

#### 4. 📰 爬虫获取失败
**问题**：无法获取新闻或获取数量为0
**解决**：
- 检查财联社网站是否可访问
- 验证User-Agent和请求头设置
- 调整请求频率避免被限制
- 检查网络连接

#### 5. 📊 收到系统状态报告邮件
**问题**：不希望收到监控邮件
**解决**：
```json
"scheduler": {
  "status_report_interval": 0  // 设置为0禁用状态报告
}
```

### 📝 日志查看
系统运行时会生成以下文件：
- `news_analysis.log` - 详细运行日志
- `news.db` - 新闻数据库
- `news_analysis.db` - 分析结果数据库
- `email_sent.db` - 邮件发送记录

### 🔧 调试模式
启用调试模式获取更详细信息：
```json
"system": {
  "debug_mode": true
},
"logging": {
  "level": "DEBUG"
}
```

## 🚀 高级使用

### 🔧 自定义分析提示词
编辑 `config.json` 中的 `system_prompt` 字段：
```json
"analyzer": {
  "system_prompt": "你是专业的A股量化投研分析师，基于四好框架..."
}
```

### 📊 添加新的新闻源
1. 在 `news_sources/` 目录创建新源模块
2. 继承 `base_source.py` 中的基类
3. 在 `config.json` 中添加新源配置
4. 在 `multi_source_manager.py` 中注册新源

### 🤖 更换AI模型
系统支持轻松切换AI模型：
```json
"analyzer": {
  "primary_provider": "deepseek",    // 改为DeepSeek优先
  "fallback_provider": "gemini"      // Gemini作为备用
}
```

### 📧 自定义邮件模板
修改 `simple_email_sender.py` 中的 `_generate_email_content` 方法来自定义邮件格式。

### ⏰ 调整运行策略
```json
"scheduler": {
  "interval_minutes": 15,           // 改为15分钟运行一次
  "working_days_only": true,        // 仅工作日运行
  "quiet_hours": {
    "start_hour": 22,               // 22点开始静默
    "end_hour": 8                   // 8点结束静默
  }
}
```

## 📋 API密钥获取

### Kimi API
1. 访问 [Moonshot AI 开放平台](https://platform.moonshot.cn/)
2. 注册账号并获取API密钥
3. 推荐使用kimi-k2-0711-preview模型
4. 支持长文本处理，适合复杂分析

### Gemini API
1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 创建新项目并获取API密钥
3. 注意：可能需要代理访问

### DeepSeek API
1. 访问 [DeepSeek开放平台](https://platform.deepseek.com/)
2. 注册账号并获取API密钥
3. 推荐使用DeepSeek R1模型

## 📝 版本信息

- **版本**: 2.0.0
- **最后更新**: 2025-01-11
- **Python版本**: 3.8+
- **主要特性**: 双AI模型、智能调度、专业投研分析

## 🤝 技术支持

如遇问题，请检查：
1. 📋 配置文件是否正确
2. 🔑 API密钥是否有效
3. 🌐 网络连接是否正常
4. 📝 日志文件中的错误信息

## 📄 许可证

本项目采用MIT许可证，可自由使用和修改。
