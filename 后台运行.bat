@echo off
chcp 65001 >nul

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🌙 AI新闻分析系统 - 后台运行                                               ║
echo ║                                                                              ║
echo ║    💡 系统将在后台持续运行                                                    ║
echo ║    📝 运行日志保存在 news_analysis.log                                       ║
echo ║    🛑 要停止系统，请运行 "停止后台运行.bat"                                   ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 🔍 检查配置文件...
if not exist "config.json" (
    echo ❌ 错误: 未找到config.json配置文件
    pause
    exit /b 1
)

echo 🔍 检查是否已有后台进程运行...
tasklist /fi "imagename eq python.exe" /fi "windowtitle eq AI新闻分析系统*" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  警告: 检测到可能已有新闻分析系统在运行
    echo 💡 如需重启，请先运行 "停止后台运行.bat"
    pause
    exit /b 1
)

echo.
echo 🚀 启动后台运行...
echo 📝 日志文件: news_analysis.log
echo 🛑 停止方法: 运行 "停止后台运行.bat"
echo.

REM 创建VBS脚本来隐藏窗口运行
echo Set WshShell = CreateObject("WScript.Shell") > temp_run.vbs
echo WshShell.Run "cmd /c python main_persistent.py", 0, False >> temp_run.vbs

REM 执行VBS脚本
cscript //nologo temp_run.vbs

REM 删除临时VBS文件
del temp_run.vbs

echo ✅ 系统已在后台启动
echo.
echo 📋 后台运行信息:
echo   🔄 运行间隔: 每30分钟
echo   🌙 静默时间: 23:00-7:00
echo   📧 结果推送: 自动发送邮件
echo   📝 运行日志: news_analysis.log
echo.
echo 💡 提示:
echo   - 系统现在在后台运行，可以关闭此窗口
echo   - 要查看运行状态，请查看日志文件
echo   - 要停止系统，请运行 "停止后台运行.bat"
echo.

pause
