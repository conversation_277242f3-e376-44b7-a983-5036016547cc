#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻分析系统 - 最简版本
主程序入口

功能：协调爬虫、分析、推送三个模块的运行
作者：虾米助手
版本：1.0.0
"""

import asyncio
import signal
import sys
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from config import load_config, setup_logging
from crawler import NewsCrawler
from analyzer import NewsAnalyzer
from pusher import EmailPusher


class NewsAnalysisSystem:
    """新闻分析系统主类"""
    
    def __init__(self):
        self.config = None
        self.logger = None
        self.crawler = None
        self.analyzer = None
        self.pusher = None
        self.running = False
    
    async def init_system(self):
        """初始化系统"""
        # 加载配置
        self.config = load_config()
        
        # 设置日志
        self.logger = setup_logging(self.config)
        self.logger.info("系统初始化开始")
        
        # 初始化各模块
        self.crawler = NewsCrawler(self.config)
        self.analyzer = NewsAnalyzer(self.config)
        self.pusher = EmailPusher(self.config)
        
        self.logger.info("系统初始化完成")
    
    async def run_analysis_workflow(self):
        """运行分析工作流"""
        self.logger.info("开始新闻分析工作流")
        
        try:
            # 1. 获取新闻
            news_list = await self.crawler.get_latest_news()
            self.logger.info(f"获取到 {len(news_list)} 条新闻")
            
            if not news_list:
                self.logger.info("没有新新闻，跳过分析")
                return
            
            # 2. 分析新闻
            analysis_results = await self.analyzer.batch_analyze(news_list)
            self.logger.info(f"完成 {len(analysis_results)} 条新闻分析")
            
            # 3. 推送结果
            if analysis_results:
                await self.pusher.send_analysis_report(analysis_results)
                self.logger.info("分析结果推送完成")
            
        except Exception as e:
            self.logger.error(f"工作流执行失败: {e}")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info("接收到退出信号，正在优雅退出...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    system = NewsAnalysisSystem()
    
    try:
        # 初始化系统
        await system.init_system()
        
        # 设置信号处理
        system.setup_signal_handlers()
        
        # 运行工作流
        system.running = True
        await system.run_analysis_workflow()
        
    except Exception as e:
        print(f"系统运行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
