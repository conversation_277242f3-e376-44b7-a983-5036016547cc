#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件推送器 - 简化版本
解决了之前邮件发送失败的问题
"""

import logging
from simple_email_sender import SimpleEmailSender

class EmailPusher:
    """邮件推送器 - 使用简单直接的发送方式"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('pusher')
        
        # 使用简单邮件发送器
        self.email_sender = SimpleEmailSender(config)
        
        self.logger.info("邮件推送器初始化完成 - 使用简化发送方式")

    async def send_analysis_report(self, analysis_results):
        """发送分析报告 - 使用简单直接的方式"""
        try:
            # 直接委托给简单邮件发送器
            await self.email_sender.send_analysis_report(analysis_results)
            self.logger.info("分析报告发送完成")
        except Exception as e:
            self.logger.error(f"发送分析报告失败: {e}")
            raise
