# AI新闻分析系统 - 持久化运行版本依赖文件
# 版本: v2.0
# 更新时间: 2025-07-09

# ===== 核心依赖（必需安装） =====
# 异步HTTP客户端 - 用于API调用和网络请求
aiohttp>=3.8.0

# 浏览器自动化 - 用于财联社新闻获取
playwright>=1.40.0

# HTML解析 - 用于网页内容提取
beautifulsoup4>=4.12.0

# ===== 可选依赖（推荐安装） =====
# HTTP请求库 - 备用网络请求方案
requests>=2.31.0

# XML/HTML解析器 - 性能更好的解析选项
lxml>=4.9.0

# ===== 开发测试依赖（开发环境） =====
# 测试框架
pytest>=7.0.0
pytest-asyncio>=0.21.0

# ===== 系统监控依赖（生产环境推荐） =====
# 系统资源监控
psutil>=5.9.0

# ===== 安全依赖（推荐） =====
# 加密和SSL支持
cryptography>=41.0.0
certifi>=2023.7.22

# ===== 标准库功能（无需安装） =====
# - sqlite3: 数据库操作（标准库）
# - smtplib: 邮件发送（标准库）
# - json: 配置文件处理（标准库）
# - logging: 日志系统（标准库）
# - asyncio: 异步处理（标准库）
# - signal: 信号处理（标准库）
# - datetime: 时间处理（标准库）
# - pathlib: 路径处理（标准库）

# ===== 安装说明 =====
# 最小安装（仅核心功能）:
#   pip install aiohttp playwright beautifulsoup4
#   playwright install chromium
#
# 完整安装（包含所有功能）:
#   pip install -r requirements.txt
#   playwright install chromium
#
# 生产环境安装（推荐）:
#   pip install aiohttp playwright beautifulsoup4 requests psutil cryptography
#   playwright install chromium
