#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多源新闻管理器

统一管理多个新闻源，支持异步并发获取
"""

import asyncio
import aiohttp
import logging
from typing import List, Dict, Any, Type
from datetime import datetime

from .base_source import BaseNewsSource, NewsItem
from .cls_source import CLSNewsSource
from .cls_browser_source import CLSBrowserNewsSource
from .bloomberg_source import BloombergNewsSource


class MultiSourceNewsManager:
    """多源新闻管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger('multi_source_manager')
        self.sources: List[BaseNewsSource] = []
        self.timeout = config.get('global_timeout', 60)
        self.max_concurrent = config.get('max_concurrent', 3)
        
        # 注册可用的新闻源
        self.source_registry = {
            'cls': CLSBrowserNewsSource,  # 使用无头浏览器版本
            'cls_api': CLSNewsSource,     # 保留API版本
            'bloomberg': BloombergNewsSource,
        }
        
        # 初始化新闻源
        self._init_news_sources()
    
    def _init_news_sources(self):
        """初始化新闻源"""
        sources_config = self.config.get('news_sources', {})
        
        for source_key, source_config in sources_config.items():
            if source_key in self.source_registry and source_config.get('enabled', True):
                try:
                    source_class = self.source_registry[source_key]
                    source_instance = source_class(source_config)
                    self.sources.append(source_instance)
                    self.logger.info(f"初始化新闻源: {source_instance.source_name}")
                except Exception as e:
                    self.logger.error(f"初始化新闻源 {source_key} 失败: {e}")
        
        self.logger.info(f"总共初始化了 {len(self.sources)} 个新闻源")
    
    async def fetch_all_news(self) -> List[NewsItem]:
        """
        异步并发获取所有新闻源的新闻
        
        Returns:
            List[NewsItem]: 所有新闻源的新闻列表
        """
        if not self.sources:
            self.logger.warning("没有可用的新闻源")
            return []
        
        self.logger.info(f"开始并发获取 {len(self.sources)} 个新闻源的新闻")
        
        # 创建HTTP会话，配置SSL以支持代理
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        connector = aiohttp.TCPConnector(ssl=False)  # 禁用SSL验证以支持代理
        async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
            # 创建并发任务
            tasks = []
            for source in self.sources:
                if source.is_enabled():
                    task = asyncio.create_task(
                        self._fetch_source_news(source, session),
                        name=f"fetch_{source.source_name}"
                    )
                    tasks.append(task)
            
            if not tasks:
                self.logger.warning("没有启用的新闻源")
                return []
            
            # 限制并发数量
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def limited_fetch(task):
                async with semaphore:
                    return await task
            
            # 等待所有任务完成
            try:
                results = await asyncio.gather(
                    *[limited_fetch(task) for task in tasks],
                    return_exceptions=True
                )
                
                # 合并所有新闻
                all_news = []
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        self.logger.error(f"新闻源 {self.sources[i].source_name} 获取失败: {result}")
                    elif isinstance(result, list):
                        all_news.extend(result)
                        self.logger.info(f"新闻源 {self.sources[i].source_name} 获取成功: {len(result)} 条")
                
                # 去重和排序
                unique_news = self._deduplicate_news(all_news)
                sorted_news = self._sort_news(unique_news)
                
                self.logger.info(f"总共获取到 {len(all_news)} 条新闻，去重后 {len(unique_news)} 条")
                
                return sorted_news
                
            except Exception as e:
                self.logger.error(f"并发获取新闻失败: {e}")
                return []
    
    async def _fetch_source_news(self, source: BaseNewsSource, session) -> List[NewsItem]:
        """
        获取单个新闻源的新闻
        
        Args:
            source: 新闻源实例
            session: HTTP会话
            
        Returns:
            List[NewsItem]: 新闻列表
        """
        try:
            start_time = datetime.now()
            news_list = await source.fetch_with_retry(session)
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            self.logger.info(f"{source.source_name} 获取耗时: {duration:.2f}秒")
            
            return news_list
            
        except Exception as e:
            self.logger.error(f"{source.source_name} 获取异常: {e}")
            return []
    
    def _deduplicate_news(self, news_list: List[NewsItem]) -> List[NewsItem]:
        """
        新闻去重
        
        Args:
            news_list: 新闻列表
            
        Returns:
            List[NewsItem]: 去重后的新闻列表
        """
        seen_titles = set()
        unique_news = []
        
        for news in news_list:
            # 使用标题的前50个字符作为去重标识
            title_key = news.title[:50].lower().strip()
            
            if title_key not in seen_titles:
                seen_titles.add(title_key)
                unique_news.append(news)
        
        return unique_news
    
    def _sort_news(self, news_list: List[NewsItem]) -> List[NewsItem]:
        """
        新闻排序
        
        Args:
            news_list: 新闻列表
            
        Returns:
            List[NewsItem]: 排序后的新闻列表
        """
        try:
            # 按时间戳降序排序（最新的在前）
            return sorted(news_list, key=lambda x: x.timestamp, reverse=True)
        except Exception as e:
            self.logger.warning(f"新闻排序失败: {e}")
            return news_list
    
    def get_sources_info(self) -> List[Dict[str, Any]]:
        """
        获取所有新闻源信息
        
        Returns:
            List[Dict[str, Any]]: 新闻源信息列表
        """
        return [source.get_source_info() for source in self.sources]
    
    def register_source(self, source_key: str, source_class: Type[BaseNewsSource]):
        """
        注册新的新闻源类型
        
        Args:
            source_key: 新闻源标识
            source_class: 新闻源类
        """
        self.source_registry[source_key] = source_class
        self.logger.info(f"注册新闻源类型: {source_key}")
    
    def add_source_instance(self, source: BaseNewsSource):
        """
        添加新闻源实例
        
        Args:
            source: 新闻源实例
        """
        self.sources.append(source)
        self.logger.info(f"添加新闻源实例: {source.source_name}")
    
    async def test_all_sources(self) -> Dict[str, Any]:
        """
        测试所有新闻源的连通性
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        results = {}
        
        timeout = aiohttp.ClientTimeout(total=30)
        connector = aiohttp.TCPConnector(ssl=False)  # 禁用SSL验证以支持代理
        async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
            for source in self.sources:
                try:
                    start_time = datetime.now()
                    news_list = await source.fetch_news(session)
                    end_time = datetime.now()
                    
                    duration = (end_time - start_time).total_seconds()
                    
                    results[source.source_name] = {
                        'status': 'success',
                        'news_count': len(news_list),
                        'duration': duration,
                        'enabled': source.is_enabled()
                    }
                    
                except Exception as e:
                    results[source.source_name] = {
                        'status': 'failed',
                        'error': str(e),
                        'enabled': source.is_enabled()
                    }
        
        return results
