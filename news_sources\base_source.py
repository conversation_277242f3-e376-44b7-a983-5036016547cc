#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻源抽象基类

定义统一的新闻源接口，支持多媒体源扩展
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any
import logging
from datetime import datetime


class NewsItem:
    """标准化新闻项"""
    
    def __init__(self, title: str, content: str, url: str, 
                 timestamp: str = None, source: str = "", 
                 category: str = "", tags: List[str] = None):
        self.title = title
        self.content = content
        self.url = url
        self.timestamp = timestamp or datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.source = source
        self.category = category
        self.tags = tags or []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'title': self.title,
            'content': self.content,
            'url': self.url,
            'timestamp': self.timestamp,
            'source': self.source,
            'category': self.category,
            'tags': self.tags
        }


class BaseNewsSource(ABC):
    """新闻源抽象基类"""
    
    def __init__(self, config: Dict[str, Any], source_name: str):
        self.config = config
        self.source_name = source_name
        self.logger = logging.getLogger(f'news_source.{source_name}')
        self.enabled = config.get('enabled', True)
        self.max_news = config.get('max_news', 10)
        self.timeout = config.get('timeout', 30)
        self.retry_times = config.get('retry_times', 3)
    
    @abstractmethod
    async def fetch_news(self, session) -> List[NewsItem]:
        """
        获取新闻的抽象方法
        
        Args:
            session: aiohttp.ClientSession 实例
            
        Returns:
            List[NewsItem]: 新闻列表
        """
        pass
    
    @abstractmethod
    def get_headers(self) -> Dict[str, str]:
        """
        获取HTTP请求头
        
        Returns:
            Dict[str, str]: 请求头字典
        """
        pass
    
    def is_enabled(self) -> bool:
        """检查新闻源是否启用"""
        return self.enabled
    
    def get_source_info(self) -> Dict[str, Any]:
        """获取新闻源信息"""
        return {
            'name': self.source_name,
            'enabled': self.enabled,
            'max_news': self.max_news,
            'timeout': self.timeout,
            'retry_times': self.retry_times
        }
    
    async def fetch_with_retry(self, session) -> List[NewsItem]:
        """
        带重试机制的新闻获取
        
        Args:
            session: aiohttp.ClientSession 实例
            
        Returns:
            List[NewsItem]: 新闻列表
        """
        if not self.is_enabled():
            self.logger.info(f"{self.source_name} 新闻源已禁用")
            return []
        
        for attempt in range(self.retry_times):
            try:
                self.logger.info(f"开始获取 {self.source_name} 新闻 (尝试 {attempt + 1}/{self.retry_times})")
                
                news_list = await self.fetch_news(session)
                
                if news_list:
                    self.logger.info(f"{self.source_name} 获取成功: {len(news_list)} 条新闻")
                    return news_list
                else:
                    self.logger.warning(f"{self.source_name} 未获取到新闻")
                    
            except Exception as e:
                self.logger.warning(f"{self.source_name} 获取失败 (尝试 {attempt + 1}/{self.retry_times}): {e}")
                if attempt == self.retry_times - 1:
                    self.logger.error(f"{self.source_name} 所有重试均失败")
        
        return []
    
    def validate_news_item(self, item: Dict[str, Any]) -> bool:
        """
        验证新闻项的有效性
        
        Args:
            item: 新闻项字典
            
        Returns:
            bool: 是否有效
        """
        required_fields = ['title', 'content', 'url']
        
        for field in required_fields:
            if not item.get(field):
                return False
        
        # 检查标题长度
        if len(item['title'].strip()) < 5:
            return False
        
        return True
    
    def clean_text(self, text: str) -> str:
        """
        清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        import re
        import html
        
        # 去除HTML实体
        text = html.unescape(text)
        
        # 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
