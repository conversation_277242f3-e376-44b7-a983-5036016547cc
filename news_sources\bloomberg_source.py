#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彭博社新闻源

基于BaseNewsSource的彭博社新闻爬虫实现
处理反爬机制和API调用
"""

import json
import hashlib
import time
from typing import List, Dict, Any
from datetime import datetime

from .base_source import BaseNewsSource, NewsItem


class BloombergNewsSource(BaseNewsSource):
    """彭博社新闻源"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config, "彭博社")
        self.api_url = config.get('api_url', 'https://www.bloomberg.com/lineup-next/api/stories')
        self.limit = min(config.get('limit', 5), self.max_news)
        self.page_number = config.get('page_number', 1)
        self.types = config.get('types', 'ARTICLE,FEATURE,INTERACTIVE,LETTER,EXPLAINERS')

        # 代理配置
        self.proxy_config = config.get('proxy', {})
        self.proxy_enabled = self.proxy_config.get('enabled', False)
        self.proxy_url = None

        if self.proxy_enabled:
            # 根据目标URL选择代理
            if self.api_url.startswith('https://'):
                self.proxy_url = self.proxy_config.get('https', self.proxy_config.get('http'))
            else:
                self.proxy_url = self.proxy_config.get('http')

            if self.proxy_url:
                self.logger.info(f"彭博社启用代理: {self.proxy_url}")
            else:
                self.logger.warning("彭博社代理配置无效，将直连")
    
    def get_headers(self) -> Dict[str, str]:
        """获取彭博社请求头（增强反爬虫处理）"""
        import random

        # 随机选择User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]

        return {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-GPC': '1'
        }
    
    async def fetch_news(self, session) -> List[NewsItem]:
        """获取彭博社新闻"""
        try:
            # 构建API参数
            params = {
                'limit': self.limit,
                'pageNumber': self.page_number,
                'types': self.types
            }

            headers = self.get_headers()

            # 添加可能的反爬参数
            headers.update(self._get_anti_bot_headers())

            # 构建请求参数
            request_kwargs = {
                'params': params,
                'headers': headers,
                'timeout': self.timeout,
                'ssl': False  # 禁用SSL验证，解决代理SSL问题
            }

            # 添加代理支持
            if self.proxy_enabled and self.proxy_url:
                request_kwargs['proxy'] = self.proxy_url
                self.logger.info(f"正在通过代理调用彭博社API: {self.api_url} (代理: {self.proxy_url})")
            else:
                self.logger.info(f"正在直连调用彭博社API: {self.api_url}")

            async with session.get(self.api_url, **request_kwargs) as response:
                self.logger.info(f"彭博社API响应状态码: {response.status}")

                if response.status == 200:
                    # 解析JSON响应
                    json_data = await response.json()
                    self.logger.info(f"成功获取JSON数据，类型: {type(json_data)}")
                    return self._parse_api_response(json_data)
                elif response.status == 403:
                    self.logger.warning(f"彭博社API返回403，被反爬虫拦截")
                    return await self._try_alternative_method(session)
                else:
                    self.logger.warning(f"彭博社API请求失败: {response.status}")
                    return await self._try_alternative_method(session)
                
        except Exception as e:
            self.logger.error(f"调用彭博社API失败: {e}")
            # 尝试备用方法
            return await self._try_alternative_method(session)
    
    def _get_anti_bot_headers(self) -> Dict[str, str]:
        """生成反爬虫检测的额外头部"""
        current_time = int(time.time())
        
        # 生成可能的签名或token（基于时间戳）
        signature = hashlib.md5(f"bloomberg{current_time}".encode()).hexdigest()
        
        return {
            'X-Timestamp': str(current_time),
            'X-Signature': signature,
            'X-Client-Version': '1.0.0',
            'X-Device-Type': 'desktop',
            'DNT': '1',
            'Sec-GPC': '1'
        }
    
    def _parse_api_response(self, json_data) -> List[NewsItem]:
        """解析彭博社API响应"""
        news_list = []

        try:
            # 彭博社API响应格式分析
            stories = []

            # 处理不同的响应格式
            if isinstance(json_data, list):
                # 直接是列表格式
                stories = json_data
                self.logger.info(f"API返回列表格式，包含 {len(stories)} 个项目")
            elif isinstance(json_data, dict):
                # 字典格式，尝试不同的字段
                if 'stories' in json_data:
                    stories = json_data['stories']
                elif 'data' in json_data:
                    data = json_data['data']
                    if isinstance(data, dict) and 'stories' in data:
                        stories = data['stories']
                    elif isinstance(data, list):
                        stories = data
                elif 'results' in json_data:
                    stories = json_data['results']
                else:
                    # 如果没有找到标准字段，尝试所有列表类型的值
                    for key, value in json_data.items():
                        if isinstance(value, list) and len(value) > 0:
                            stories = value
                            self.logger.info(f"在字段 '{key}' 中找到列表数据")
                            break
            
            if not stories:
                self.logger.warning("彭博社API响应中未找到新闻数据")
                return news_list
            
            # 解析每条新闻
            for i, item in enumerate(stories[:self.max_news]):
                if not isinstance(item, dict):
                    self.logger.debug(f"跳过非字典项目 {i}: {type(item)}")
                    continue

                self.logger.debug(f"解析项目 {i}: {list(item.keys()) if isinstance(item, dict) else 'N/A'}")

                # 提取新闻字段 - 尝试更多可能的字段名
                title = (item.get('title', '') or
                        item.get('headline', '') or
                        item.get('name', '') or
                        item.get('shortHeadline', '') or
                        item.get('longHeadline', ''))

                summary = (item.get('summary', '') or
                          item.get('description', '') or
                          item.get('abstract', '') or
                          item.get('excerpt', '') or
                          item.get('lead', ''))

                url = (item.get('url', '') or
                      item.get('link', '') or
                      item.get('href', '') or
                      item.get('uri', ''))
                
                # 时间处理
                published_time = item.get('publishedAt', '') or item.get('createdAt', '') or item.get('timestamp', '')
                if published_time:
                    try:
                        # 尝试解析ISO格式时间
                        if 'T' in published_time:
                            dt = datetime.fromisoformat(published_time.replace('Z', '+00:00'))
                            timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            timestamp = published_time
                    except:
                        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                else:
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 构建完整URL
                if url and not url.startswith('http'):
                    url = f"https://www.bloomberg.com{url}"
                elif not url:
                    url = "https://www.bloomberg.com"
                
                # 验证和创建新闻项
                if title and len(title.strip()) > 5:
                    news_item = NewsItem(
                        title=self.clean_text(title),
                        content=self.clean_text(summary) or f"Bloomberg: {title}",
                        url=url,
                        timestamp=timestamp,
                        source="彭博社",
                        category="国际财经",
                        tags=["Bloomberg", "国际新闻", "财经"]
                    )
                    news_list.append(news_item)
                    self.logger.debug(f"成功解析新闻: {title[:50]}...")
                else:
                    self.logger.debug(f"跳过无效新闻项: title='{title}', 长度={len(title) if title else 0}")

            self.logger.info(f"彭博社API解析获取到 {len(news_list)} 条新闻")
            
        except Exception as e:
            self.logger.error(f"彭博社API响应解析失败: {e}")
        
        return news_list
    
    async def _try_alternative_method(self, session) -> List[NewsItem]:
        """尝试备用方法获取彭博社新闻"""
        try:
            # 备用方法：访问主页并解析
            main_url = "https://www.bloomberg.com"
            headers = self.get_headers()
            headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'

            # 构建请求参数
            request_kwargs = {
                'headers': headers,
                'timeout': self.timeout,
                'ssl': False  # 禁用SSL验证
            }

            # 添加代理支持
            if self.proxy_enabled and self.proxy_url:
                request_kwargs['proxy'] = self.proxy_url
                self.logger.info(f"尝试彭博社备用方法（通过代理）: {main_url}")
            else:
                self.logger.info(f"尝试彭博社备用方法（直连）: {main_url}")

            async with session.get(main_url, **request_kwargs) as response:
                if response.status == 200:
                    html_content = await response.text()
                    return self._parse_html_content(html_content)
                elif response.status == 403:
                    self.logger.warning(f"彭博社备用方法也被反爬虫拦截: {response.status}")
                    return []
                else:
                    self.logger.warning(f"彭博社备用方法失败: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"彭博社备用方法异常: {e}")
        
        return []
    
    def _parse_html_content(self, html_content: str) -> List[NewsItem]:
        """解析彭博社HTML内容"""
        import re
        
        news_list = []
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            # 彭博社HTML解析模式
            patterns = [
                r'"headline":"([^"]+)"',
                r'"title":"([^"]+)"',
                r'<h[1-6][^>]*>([^<]+)</h[1-6]>',
                r'data-module="Headline"[^>]*>([^<]+)<',
            ]
            
            all_titles = set()
            
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    title = self.clean_text(match)
                    if title and len(title) > 10 and title not in all_titles:
                        all_titles.add(title)
                        
                        news_item = NewsItem(
                            title=title,
                            content=f"Bloomberg: {title}",
                            url="https://www.bloomberg.com",
                            timestamp=current_time,
                            source="彭博社",
                            category="国际财经",
                            tags=["Bloomberg", "国际新闻"]
                        )
                        news_list.append(news_item)
                        
                        if len(news_list) >= self.max_news:
                            break
                
                if len(news_list) >= self.max_news:
                    break
            
            self.logger.info(f"彭博社HTML解析获取到 {len(news_list)} 条新闻")
            
        except Exception as e:
            self.logger.error(f"彭博社HTML解析失败: {e}")
        
        return news_list[:self.max_news]


