# 🤖 Kimi 模型配置指南

## 📋 概述

本指南将帮助您配置 Kimi K2 模型来替代 DeepSeek 进行新闻分析。Kimi K2 是 Moonshot AI 推出的最新大语言模型，具有强大的中文理解和分析能力。

## 🔑 获取 Kimi API 密钥

### 步骤 1：注册账号
1. 访问 [Moonshot AI 开放平台](https://platform.moonshot.cn/)
2. 点击"注册"按钮创建新账号
3. 完成邮箱验证和实名认证

### 步骤 2：获取 API 密钥
1. 登录后进入控制台
2. 在左侧菜单中选择"API 密钥"
3. 点击"创建新密钥"
4. 复制生成的 API 密钥（格式：sk-xxxxxxxxxxxxxxxx）

### 步骤 3：充值账户
1. 在控制台中选择"账户余额"
2. 根据需要充值（建议先充值少量测试）
3. Kimi API 按 token 使用量计费

## ⚙️ 配置文件修改

### 方法 1：使用示例配置文件
1. 复制 `config_kimi_example.json` 为 `config.json`
2. 修改其中的 API 密钥：
```json
{
  "analyzer": {
    "primary_provider": "kimi",
    "kimi": {
      "api_key": "sk-your-actual-kimi-api-key-here"
    }
  }
}
```

### 方法 2：修改现有配置
在现有的 `config.json` 中添加 Kimi 配置：

```json
{
  "analyzer": {
    "primary_provider": "kimi",
    "fallback_provider": "deepseek",
    "kimi": {
      "api_key": "sk-your-kimi-api-key-here",
      "api_url": "https://api.moonshot.cn/v1/chat/completions",
      "model": "kimi-k2-0711-preview",
      "enabled": true,
      "max_tokens": 4000,
      "temperature": 0.6
    }
  }
}
```

## 🎛️ 参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `api_key` | Kimi API 密钥 | 从平台获取 |
| `api_url` | API 端点地址 | `https://api.moonshot.cn/v1/chat/completions` |
| `model` | 模型名称 | `kimi-k2-0711-preview` |
| `max_tokens` | 最大输出 token 数 | `4000` |
| `temperature` | 温度参数（控制创造性） | `0.6` |

## 🔄 模型切换策略

### 主要模型 + 备用模型
- **主要模型**：`kimi` - 用于日常分析
- **备用模型**：`deepseek` - 当 Kimi 失败时自动切换

### 完全替换 DeepSeek
如果您想完全使用 Kimi，可以设置：
```json
{
  "primary_provider": "kimi",
  "fallback_provider": "kimi"
}
```

### 保留多模型选择
保留所有模型配置，可以随时切换：
```json
{
  "primary_provider": "kimi",     // 当前使用 Kimi
  "fallback_provider": "deepseek", // 备用 DeepSeek
  "kimi": { ... },                // Kimi 配置
  "deepseek": { ... },            // DeepSeek 配置（保留）
  "gemini": { ... }               // Gemini 配置（保留）
}
```

## 🧪 测试配置

### 启动测试
1. 保存配置文件
2. 运行测试命令：
```bash
python main.py
```

### 检查日志
观察日志输出，确认：
- ✅ Kimi API 连接成功
- ✅ 模型响应正常
- ✅ 分析结果完整

### 常见问题排查
1. **API 密钥错误**：检查密钥格式和有效性
2. **余额不足**：检查账户余额
3. **网络连接**：确认网络可以访问 api.moonshot.cn
4. **参数错误**：检查 model 名称是否正确

## 💰 成本优化

### Token 使用优化
- 调整 `max_tokens` 参数控制输出长度
- 优化 system_prompt 减少不必要的输出

### 批量处理
- 利用系统的批量分析功能
- 避免重复分析相同新闻

### 监控使用量
- 定期检查 API 使用量
- 设置合理的使用限制

## 🔧 高级配置

### 自定义提示词
可以针对 Kimi 模型特点优化 system_prompt：
```json
{
  "system_prompt": "你是专业的A股投资分析师，擅长从新闻中挖掘投资机会..."
}
```

### 性能调优
- `temperature`: 0.3-0.8（分析类任务推荐 0.6）
- `max_tokens`: 2000-8000（根据需要调整）
- `timeout`: 60-180秒（根据网络情况）

## 📞 技术支持

如遇问题，请：
1. 检查日志文件 `news_analysis.log`
2. 参考 [Moonshot AI 文档](https://platform.moonshot.cn/docs)
3. 联系技术支持

---

**注意**：请妥善保管您的 API 密钥，不要在公开场所分享。
