#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持久化新闻分析系统启动脚本
"""

import asyncio
import sys
import argparse
from pathlib import Path
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    🧠 AI新闻分析系统 - 持久化运行版本                                          ║
║                                                                              ║
║    🚀 特性:                                                                   ║
║      • Google Gemini 2.5 Pro思考模型深度分析                                 ║
║      • 财联社加红栏目 + 彭博社双源新闻                                        ║
║      • 智能调度和错误处理                                                     ║
║      • 实时状态监控和报告                                                     ║
║      • 优雅停止和重启机制                                                     ║
║                                                                              ║
║    ⚙️  配置: config.json -> scheduler 部分                                    ║
║    📧 推送: 自动邮件通知分析结果                                              ║
║    🛑 停止: Ctrl+C 优雅停止                                                   ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def print_config_info():
    """打印配置信息"""
    try:
        from config import load_config
        config = load_config()
        scheduler_config = config.get('scheduler', {})
        
        print("📋 当前配置:")
        print(f"  🔄 运行间隔: {scheduler_config.get('interval_minutes', 30)} 分钟")
        print(f"  🎯 最大运行次数: {'无限制' if scheduler_config.get('max_runs', 0) == 0 else scheduler_config.get('max_runs', 0)}")
        print(f"  🌙 静默时间: {scheduler_config.get('quiet_hours', {}).get('start_hour', 23)}:00 - {scheduler_config.get('quiet_hours', {}).get('end_hour', 7)}:00")
        print(f"  📅 仅工作日: {'是' if scheduler_config.get('working_days_only', False) else '否'}")
        print(f"  📊 状态报告间隔: 每 {scheduler_config.get('status_report_interval', 6)} 次运行")
        print(f"  📰 每次最大新闻数: {scheduler_config.get('max_news_per_run', 20)} 条")
        print(f"  ⚡ 启动时运行: {'是' if scheduler_config.get('run_on_startup', True) else '否'}")
        
        # AI模型配置
        analyzer_config = config.get('analyzer', {})
        gemini_config = analyzer_config.get('gemini', {})
        print(f"\n🧠 AI配置:")
        print(f"  🎯 主要模型: {analyzer_config.get('primary_provider', 'gemini')}")
        print(f"  🔄 备用模型: {analyzer_config.get('fallback_provider', 'deepseek')}")
        print(f"  🤖 Gemini模型: {gemini_config.get('model', 'unknown')}")
        print(f"  ⏱️  思考超时: {gemini_config.get('thinking_timeout', 600)} 秒")
        
        # 新闻源配置
        news_sources = config.get('news_sources', {})
        enabled_sources = [name for name, cfg in news_sources.items() if cfg.get('enabled', False)]
        print(f"\n📰 新闻源:")
        print(f"  📡 启用的源: {', '.join(enabled_sources) if enabled_sources else '无'}")
        
        # 推送配置
        pusher_config = config.get('pusher', {})
        print(f"\n📧 推送配置:")
        print(f"  📬 邮件推送: {'启用' if pusher_config.get('enabled', False) else '禁用'}")
        print(f"  📮 收件人: {pusher_config.get('to_email', '未配置')}")
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")

async def run_test_mode():
    """运行测试模式"""
    print("\n🧪 测试模式启动...")
    
    try:
        from main_persistent import PersistentNewsAnalysisSystem
        
        system = PersistentNewsAnalysisSystem()
        
        # 初始化系统
        if not await system.init_system():
            print("❌ 系统初始化失败")
            return False
        
        print("✅ 系统初始化成功")
        
        # 运行单次分析
        print("🔄 运行单次分析测试...")
        success = await system.run_single_analysis()
        
        if success:
            print("✅ 测试模式运行成功！")
            print("🚀 可以启动持久化运行模式")
        else:
            print("❌ 测试模式运行失败")
            print("🔧 请检查配置和网络连接")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试模式异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_persistent_mode():
    """运行持久化模式"""
    print("\n🔄 持久化运行模式启动...")
    
    try:
        from main_persistent import PersistentNewsAnalysisSystem
        
        system = PersistentNewsAnalysisSystem()
        
        # 初始化系统
        if not await system.init_system():
            print("❌ 系统初始化失败")
            return False
        
        print("✅ 系统初始化成功")
        print("🔄 开始持久化运行...")
        print("💡 提示: 按 Ctrl+C 优雅停止系统")
        
        # 运行持久化分析
        await system.run_persistent()
        
        return True
        
    except KeyboardInterrupt:
        print("\n📡 收到停止信号，系统正在优雅关闭...")
        return True
    except Exception as e:
        print(f"❌ 持久化运行异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI新闻分析系统 - 持久化运行版本')
    parser.add_argument('--mode', choices=['test', 'run'], default='run',
                       help='运行模式: test=测试模式(单次运行), run=持久化运行模式')
    parser.add_argument('--config', action='store_true',
                       help='显示当前配置信息')
    parser.add_argument('--no-banner', action='store_true',
                       help='不显示启动横幅')
    
    args = parser.parse_args()
    
    # 显示横幅
    if not args.no_banner:
        print_banner()
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 运行模式: {'测试模式' if args.mode == 'test' else '持久化运行模式'}")
    
    # 显示配置信息
    if args.config or not args.no_banner:
        print_config_info()
    
    if args.config:
        return
    
    # 运行系统
    try:
        if args.mode == 'test':
            success = asyncio.run(run_test_mode())
            sys.exit(0 if success else 1)
        else:
            success = asyncio.run(run_persistent_mode())
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        print("\n📡 收到中断信号，系统退出")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
