#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kimi API 速率限制测试脚本

功能：测试 Kimi API 的速率限制处理和重试机制
"""

import asyncio
import logging
from config import load_config, setup_logging
from analyzer import NewsAnalyzer

# 测试新闻数据
TEST_NEWS = [
    {
        "title": "测试新闻1：某科技公司发布新产品",
        "content": "某知名科技公司今日发布了一款革命性的新产品，预计将对行业产生重大影响。该产品采用了最新的技术，具有多项创新功能。",
        "url": "https://test.com/news1",
        "publish_time": "2024-01-01 10:00:00"
    },
    {
        "title": "测试新闻2：市场监管部门发布新政策",
        "content": "市场监管部门今日发布了新的行业监管政策，旨在规范市场秩序，保护消费者权益。新政策将于下月正式实施。",
        "url": "https://test.com/news2", 
        "publish_time": "2024-01-01 11:00:00"
    },
    {
        "title": "测试新闻3：某上市公司发布财报",
        "content": "某知名上市公司发布了最新季度财报，营收和利润均超出市场预期。公司管理层对未来发展前景表示乐观。",
        "url": "https://test.com/news3",
        "publish_time": "2024-01-01 12:00:00"
    }
]

async def test_single_analysis():
    """测试单个新闻分析"""
    print("🧪 测试单个新闻分析...")
    
    config = load_config()
    logger = setup_logging(config)
    analyzer = NewsAnalyzer(config)
    
    try:
        result = await analyzer.analyze_news(TEST_NEWS[0])
        print("✅ 单个新闻分析成功")
        print(f"📊 分析结果长度: {len(result.get('markdown_report', ''))}")
        print(f"🤖 使用模型: {result.get('model_used', 'unknown')}")
        return True
    except Exception as e:
        print(f"❌ 单个新闻分析失败: {e}")
        return False

async def test_batch_analysis():
    """测试批量新闻分析"""
    print("\n🧪 测试批量新闻分析...")
    
    config = load_config()
    logger = setup_logging(config)
    analyzer = NewsAnalyzer(config)
    
    try:
        results = await analyzer.batch_analyze(TEST_NEWS)
        print(f"✅ 批量分析完成，成功分析 {len(results)} 条新闻")
        
        for i, result in enumerate(results):
            analysis = result.get('analysis', {})
            print(f"📰 新闻 {i+1}: {analysis.get('model_used', 'unknown')} - {len(analysis.get('markdown_report', ''))} 字符")
        
        return True
    except Exception as e:
        print(f"❌ 批量分析失败: {e}")
        return False

async def test_rate_limit_handling():
    """测试速率限制处理"""
    print("\n🧪 测试速率限制处理...")
    
    config = load_config()
    logger = setup_logging(config)
    analyzer = NewsAnalyzer(config)
    
    # 快速连续发送多个请求来触发速率限制
    tasks = []
    for i in range(3):
        tasks.append(analyzer.analyze_news(TEST_NEWS[i % len(TEST_NEWS)]))
    
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = 0
        error_count = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"❌ 请求 {i+1} 失败: {result}")
                error_count += 1
            else:
                print(f"✅ 请求 {i+1} 成功: {result.get('model_used', 'unknown')}")
                success_count += 1
        
        print(f"📊 测试结果: {success_count} 成功, {error_count} 失败")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 速率限制测试失败: {e}")
        return False

async def test_configuration():
    """测试配置参数"""
    print("\n🧪 测试配置参数...")
    
    config = load_config()
    analyzer_config = config.get('analyzer', {})
    kimi_config = analyzer_config.get('kimi', {})
    
    print(f"🎯 主要模型: {analyzer_config.get('primary_provider', 'unknown')}")
    print(f"🔄 备用模型: {analyzer_config.get('fallback_provider', 'unknown')}")
    print(f"📦 批次大小: {analyzer_config.get('batch_size', 'unknown')}")
    print(f"⏱️  超时时间: {analyzer_config.get('timeout', 'unknown')} 秒")
    print(f"🔁 最大重试: {analyzer_config.get('max_retries', 'unknown')} 次")
    
    print(f"\n🤖 Kimi 配置:")
    print(f"  🔑 API密钥: {'已配置' if kimi_config.get('api_key') else '未配置'}")
    print(f"  🌐 API地址: {kimi_config.get('api_url', 'unknown')}")
    print(f"  🎭 模型名称: {kimi_config.get('model', 'unknown')}")
    print(f"  📝 最大Token: {kimi_config.get('max_tokens', 'unknown')}")
    print(f"  🌡️  温度参数: {kimi_config.get('temperature', 'unknown')}")
    print(f"  ⏰ 请求间隔: {kimi_config.get('request_interval', 'unknown')} 秒")
    print(f"  🔢 最大并发: {kimi_config.get('max_concurrent', 'unknown')}")
    
    # 检查关键配置
    issues = []
    if not kimi_config.get('api_key'):
        issues.append("❌ Kimi API密钥未配置")
    if kimi_config.get('max_tokens', 0) <= 0:
        issues.append("❌ max_tokens 配置无效")
    if kimi_config.get('request_interval', 0) < 1:
        issues.append("⚠️ request_interval 可能过小，建议 >= 1.5 秒")
    
    if issues:
        print("\n🚨 配置问题:")
        for issue in issues:
            print(f"  {issue}")
        return False
    else:
        print("\n✅ 配置检查通过")
        return True

async def main():
    """主测试函数"""
    print("🚀 开始 Kimi API 速率限制测试")
    print("=" * 50)
    
    # 设置日志级别
    logging.getLogger('analyzer').setLevel(logging.INFO)
    
    test_results = []
    
    # 1. 测试配置
    config_ok = await test_configuration()
    test_results.append(("配置检查", config_ok))
    
    if not config_ok:
        print("\n❌ 配置检查失败，请修复配置后重试")
        return
    
    # 2. 测试单个分析
    single_ok = await test_single_analysis()
    test_results.append(("单个分析", single_ok))
    
    # 3. 测试批量分析
    batch_ok = await test_batch_analysis()
    test_results.append(("批量分析", batch_ok))
    
    # 4. 测试速率限制处理
    rate_limit_ok = await test_rate_limit_handling()
    test_results.append(("速率限制处理", rate_limit_ok))
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！Kimi API 配置正常")
    else:
        print("\n⚠️ 部分测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    asyncio.run(main())
