{
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  },
  "crawler": {
    "timeout": 30,
    "max_news": 10,
    "retry_times": 3,
    "interval": 300,
    "enabled": true
  },
  "news_sources": {
    "cls": {
      "enabled": true,
      "url": "https://www.cls.cn/api/sw?app=CailianpressWeb&os=web&sv=7.7.5&sign=b9b8c6b4c12c6be4a4c6e4f4e4f4e4f4",
      "timeout": 30,
      "max_news": 10
    }
  },
  "analyzer": {
    "primary_provider": "kimi",
    "fallback_provider": "deepseek",
    "batch_size": 5,
    "timeout": 120,
    "max_retries": 3,
    "allow_mock_analysis": false,
    "kimi": {
      "api_key": "sk-your-kimi-api-key-here",
      "api_url": "https://api.moonshot.cn/v1/chat/completions",
      "model": "kimi-k2-0711-preview",
      "enabled": true,
      "max_tokens": 4000,
      "temperature": 0.6
    },
    "gemini": {
      "api_key": "your-gemini-api-key-here",
      "api_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent",
      "model": "gemini-2.5-pro",
      "enabled": true,
      "max_tokens": 8192,
      "temperature": 0.7,
      "topP": 0.95,
      "topK": 40,
      "proxy_url": "http://127.0.0.1:7897",
      "thinking_timeout": 600,
      "max_thinking_tokens": 65536,
      "wait_for_complete_thinking": true
    },
    "deepseek": {
      "api_key": "sk-your-deepseek-api-key-here",
      "api_url": "https://api.deepseek.com/chat/completions",
      "model": "deepseek-reasoner",
      "enabled": true,
      "max_tokens": 4000,
      "temperature": 0.1
    },
    "system_prompt": "角色：事件驱动投资分析专家\n任务：对任意新闻做 8 步投资分析并给出行动建议；信息不足须先提问。\n必须遵守：\n1. 只用事实+明示假设，不臆断；2. 分级列点；3. 缺信息用"需××"标注；\n4. 风险至少 3 条；5. 引用或估算数据请注明来源/可靠性。\n\n分析框架：\n① 事件概述（≤3 句）\n② 关键特征/约束（技术、政策、时空、替代方案）\n③ 行为改变（政府/企业/居民）\n④ 供需错配（需求↑↓；供给限制；价格/库存/交付）\n⑤ 行业映射（受益/受损，排序+逻辑）\n⑥ 标的筛选（业务纯度、份额、弹性）\n⑦ 财务与估值弹性（关键假设+业绩/估值变化）\n⑧ 投资结论（主推标的、仓位、持有期、催化剂时间轴、核心风险）"
  },
  "pusher": {
    "smtp_server": "smtp.qq.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your_email_password",
    "to_email": "<EMAIL>",
    "enabled": true,
    "quiet_hours": {
      "start_hour": 22,
      "end_hour": 8
    }
  },
  "database": {
    "path": "news_analysis.db",
    "backup_enabled": true,
    "backup_interval": 86400
  }
}
