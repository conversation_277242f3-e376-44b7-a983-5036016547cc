#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块

功能：统一的配置文件加载和管理
特点：自包含，易于复制粘贴
"""

import json
import logging
import os
from pathlib import Path


def load_config(config_file="config.json"):
    """加载配置文件"""
    config_path = Path(__file__).parent / config_file
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        # 返回默认配置
        return get_default_config()
    except json.JSONDecodeError as e:
        raise ValueError(f"配置文件格式错误: {e}")


def get_config(config, key, default=None):
    """获取嵌套配置值，支持点号分隔"""
    keys = key.split('.')
    value = config
    
    for k in keys:
        if isinstance(value, dict) and k in value:
            value = value[k]
        else:
            return default
    
    return value


def setup_logging(config):
    """设置日志系统"""
    log_level = get_config(config, 'logging.level', 'INFO')
    log_format = get_config(config, 'logging.format', 
                           '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('news_analysis.log', encoding='utf-8')
        ]
    )
    
    return logging.getLogger('news_analysis')


def get_default_config():
    """获取默认配置"""
    return {
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        },
        "crawler": {
            "timeout": 30,
            "max_news": 10,
            "retry_times": 3
        },
        "analyzer": {
            "api_key": "",
            "batch_size": 5,
            "timeout": 60
        },
        "pusher": {
            "smtp_server": "smtp.qq.com",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "to_email": ""
        }
    }
