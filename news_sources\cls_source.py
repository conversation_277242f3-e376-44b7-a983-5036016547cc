#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财联社新闻源

基于BaseNewsSource的财联社新闻爬虫实现
"""

import re
import asyncio
from typing import List, Dict, Any
from datetime import datetime

from .base_source import BaseNewsSource, NewsItem


class CLSNewsSource(BaseNewsSource):
    """财联社新闻源"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config, "财联社")
        self.base_url = config.get('base_url', 'https://www.cls.cn/telegraph')
        self.api_url = config.get('api_url', 'https://www.cls.cn/v1/roll/get_roll_list')

        # API参数配置
        self.api_params = {
            'app': config.get('app', 'CailianpressWeb'),
            'category': config.get('category', 'red'),
            'os': config.get('os', 'web'),
            'refresh_type': config.get('refresh_type', '1'),
            'rn': str(min(config.get('rn', 20), self.max_news)),
            'sv': config.get('sv', '8.4.6')
        }

        # 签名配置
        self.use_api = config.get('use_api', True)
        self.use_fixed_sign = config.get('use_fixed_sign', True)

        # 已知有效的签名和时间戳
        self.known_signatures = [
            {
                'timestamp': '1752044292',
                'sign': '31f9acde58486eb1d6f50de6b2b4ab45'
            },
            {
                'timestamp': '1752046649',
                'sign': 'cd110018cfb87da5391104387bb2ed47'
            }
        ]
    
    def get_headers(self) -> Dict[str, str]:
        """获取财联社请求头（HTML）"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }

    def get_api_headers(self) -> Dict[str, str]:
        """获取财联社API请求头（绕过WAF）"""
        import random

        # 随机选择User-Agent，模拟真实浏览器
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        ]

        return {
            'User-Agent': random.choice(user_agents),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.cls.cn/telegraph',
            'Origin': 'https://www.cls.cn',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }

    def _generate_sign(self, params: dict) -> str:
        """生成动态签名"""
        import hashlib
        import time

        # 获取当前时间戳
        current_time = int(time.time())

        # 尝试多种签名算法
        sign_methods = [
            # 方法1: MD5(所有参数按字母排序 + 密钥)
            lambda: self._sign_method_1(params, current_time),
            # 方法2: MD5(app + timestamp + 密钥)
            lambda: self._sign_method_2(params, current_time),
            # 方法3: MD5(参数值拼接 + 密钥)
            lambda: self._sign_method_3(params, current_time),
        ]

        # 返回第一种方法的签名
        return sign_methods[0]()

    def _sign_method_1(self, params: dict, timestamp: int) -> str:
        """签名方法1: 参数排序 + 密钥"""
        import hashlib

        # 添加时间戳到参数
        sign_params = params.copy()
        sign_params['last_time'] = str(timestamp)

        # 按键名排序并拼接
        sorted_params = sorted(sign_params.items())
        param_str = '&'.join([f"{k}={v}" for k, v in sorted_params])

        # 添加密钥
        sign_string = f"{param_str}&key={self.sign_key}"

        # 生成MD5签名
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()

    def _sign_method_2(self, params: dict, timestamp: int) -> str:
        """签名方法2: app + timestamp + 密钥"""
        import hashlib

        app = params.get('app', 'CailianpressWeb')
        sign_string = f"{app}{timestamp}{self.sign_key}"

        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()

    def _sign_method_3(self, params: dict, timestamp: int) -> str:
        """签名方法3: 参数值拼接 + 密钥"""
        import hashlib

        # 按固定顺序拼接参数值
        values = [
            params.get('app', 'CailianpressWeb'),
            params.get('category', 'red'),
            str(timestamp),
            params.get('os', 'web'),
            params.get('refresh_type', '1'),
            params.get('rn', '10'),
            params.get('sv', '8.4.6')
        ]

        sign_string = ''.join(values) + self.sign_key

        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    async def fetch_news(self, session) -> List[NewsItem]:
        """获取财联社新闻（只使用API接口）"""
        try:
            return await self._fetch_api_news(session)
        except Exception as e:
            self.logger.error(f"获取财联社新闻失败: {e}")
            return []

    async def _fetch_api_news(self, session) -> List[NewsItem]:
        """通过API获取新闻（绕过WAF）"""
        try:
            # 先访问主页建立会话
            await self._establish_session(session)

            # 尝试使用已知有效的签名
            for signature_data in self.known_signatures:
                try:
                    params = self.api_params.copy()
                    params['last_time'] = signature_data['timestamp']
                    params['sign'] = signature_data['sign']

                    headers = self.get_api_headers()

                    self.logger.info(f"正在调用财联社API: {self.api_url}")
                    self.logger.info(f"使用签名: {signature_data['sign'][:8]}... (时间戳: {signature_data['timestamp']})")

                    # 添加随机延迟，模拟人类行为
                    import random
                    await asyncio.sleep(random.uniform(0.5, 1.5))

                    async with session.get(self.api_url, params=params, headers=headers, timeout=self.timeout) as response:
                        self.logger.info(f"财联社API响应状态码: {response.status}")

                        if response.status == 418:
                            self.logger.warning("财联社API被WAF拦截，尝试下一个签名")
                            continue
                        elif response.status != 200:
                            self.logger.warning(f"财联社API请求失败: {response.status}，尝试下一个签名")
                            continue

                        # 解析JSON响应
                        json_data = await response.json()
                        errno = json_data.get('errno', 'unknown')

                        if errno == 0 or errno == '0':
                            self.logger.info(f"✅ 签名有效！成功获取JSON数据")
                            return self._parse_api_response(json_data)
                        else:
                            msg = json_data.get('msg', 'unknown')
                            self.logger.warning(f"签名失效: {msg}，尝试下一个签名")
                            continue

                except Exception as e:
                    self.logger.warning(f"签名 {signature_data['sign'][:8]}... 测试失败: {e}")
                    continue

            # 所有已知签名都失败
            self.logger.error("所有已知签名都已失效")
            return []

        except Exception as e:
            self.logger.error(f"调用财联社API失败: {e}")
            return []

    async def _establish_session(self, session):
        """建立会话，访问主页获取cookies"""
        try:
            headers = self.get_headers()
            self.logger.debug("建立财联社会话...")

            async with session.get(self.base_url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    self.logger.debug("会话建立成功")
                else:
                    self.logger.warning(f"会话建立失败: {response.status}")
        except Exception as e:
            self.logger.warning(f"建立会话异常: {e}")



    def _parse_api_response(self, json_data) -> List[NewsItem]:
        """解析财联社API响应"""
        news_list = []

        try:
            self.logger.debug(f"API响应数据结构: {type(json_data)}")

            # 检查API响应格式
            if not isinstance(json_data, dict):
                self.logger.error("API响应格式不正确，不是字典类型")
                return news_list

            # 检查错误码
            errno = json_data.get('errno')
            if errno and errno != '0':
                error_msg = json_data.get('msg', '未知错误')
                self.logger.error(f"API返回错误: errno={errno}, msg={error_msg}")
                return news_list

            # 获取新闻数据列表
            data = json_data.get('data', {})
            if not isinstance(data, dict):
                self.logger.error("API响应中data字段格式不正确")
                return news_list

            roll_data = data.get('roll_data', [])
            if not isinstance(roll_data, list):
                self.logger.error("API响应中roll_data字段格式不正确")
                return news_list

            self.logger.info(f"API返回 {len(roll_data)} 条新闻数据")

            # 解析每条新闻
            for i, item in enumerate(roll_data[:self.max_news]):
                if not isinstance(item, dict):
                    self.logger.debug(f"跳过非字典项目 {i}")
                    continue

                # 提取新闻字段
                title = item.get('title', '') or item.get('brief', '')
                content = item.get('content', '') or item.get('brief', '') or title

                # 时间戳转换
                ctime = item.get('ctime', 0)
                if ctime:
                    try:
                        if len(str(ctime)) == 10:  # 秒级时间戳
                            timestamp = datetime.fromtimestamp(ctime).strftime('%Y-%m-%d %H:%M:%S')
                        else:  # 毫秒级时间戳
                            timestamp = datetime.fromtimestamp(ctime/1000).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                else:
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 构建新闻URL
                news_id = item.get('id', '') or item.get('news_id', '')
                if news_id:
                    url = f"https://www.cls.cn/detail/{news_id}"
                else:
                    url = f"https://www.cls.cn/telegraph#{i+1}"

                # 验证和创建新闻项
                if title and len(title.strip()) > 5:
                    news_item = NewsItem(
                        title=self.clean_text(title),
                        content=self.clean_text(content) or f"财联社消息，{title}。",
                        url=url,
                        timestamp=timestamp,
                        source="财联社",
                        category="财经",
                        tags=["财联社", "财经新闻", "API"]
                    )
                    news_list.append(news_item)
                    self.logger.debug(f"成功解析新闻: {title[:50]}...")
                else:
                    self.logger.debug(f"跳过无效新闻项: title='{title}', 长度={len(title) if title else 0}")

            self.logger.info(f"财联社API解析获取到 {len(news_list)} 条有效新闻")

        except Exception as e:
            self.logger.error(f"财联社API响应解析失败: {e}")

        return news_list

    def _parse_html_content(self, html_content: str) -> List[NewsItem]:
        """解析HTML内容"""
        news_list = []
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            # 多种新闻提取模式
            news_patterns = [
                r'"title":"([^"]+)"',
                r'"content":"([^"]+)"',
                r'<span[^>]*class="[^"]*title[^"]*"[^>]*>([^<]+)</span>',
                r'data-title="([^"]+)"',
                r'<div[^>]*class="[^"]*news[^"]*title[^"]*"[^>]*>([^<]+)</div>',
            ]
            
            all_news_titles = set()
            
            for i, pattern in enumerate(news_patterns, 1):
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    self.logger.debug(f"模式{i}找到 {len(matches)} 条新闻")
                    
                    for match in matches:
                        title = self._clean_news_title(match)
                        if title and len(title) > 10 and title not in all_news_titles:
                            all_news_titles.add(title)
                            
                            news_item = NewsItem(
                                title=title,
                                content=f"财联社消息，{title}。",
                                url=f"https://www.cls.cn/telegraph#{len(news_list)+1}",
                                timestamp=current_time,
                                source="财联社",
                                category="财经",
                                tags=["财联社", "财经新闻"]
                            )
                            news_list.append(news_item)
                            
                            if len(news_list) >= self.max_news:
                                break
                
                if len(news_list) >= self.max_news:
                    break
            
            self.logger.info(f"财联社HTML解析获取到 {len(news_list)} 条有效新闻")
            
        except Exception as e:
            self.logger.error(f"财联社HTML解析失败: {e}")
        
        return news_list[:self.max_news]
    
    def _clean_news_title(self, title: str) -> str:
        """清理新闻标题"""
        if not title:
            return ""
        
        title = self.clean_text(title)
        
        # 过滤无效标题
        invalid_patterns = [
            r'^[\s\d\-_]+$',
            r'^[a-zA-Z\s]+$',
            r'^\W+$',
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, title):
                return ""
        
        # 检查是否包含中文字符
        if not re.search(r'[\u4e00-\u9fff]', title):
            return ""
        
        return title
