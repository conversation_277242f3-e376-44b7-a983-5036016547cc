#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
思考模型分析器 - 专门处理Gemini 2.5 Pro思考模型
"""

import asyncio
import aiohttp
import json
import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

class ThinkingModelAnalyzer:
    """思考模型分析器 - 专门处理需要长时间思考的模型"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 思考模型特殊配置
        self.thinking_timeout = config.get('thinking_timeout', 600)  # 10分钟
        self.max_thinking_tokens = config.get('max_thinking_tokens', 65536)
        self.wait_for_complete_thinking = config.get('wait_for_complete_thinking', True)
        
        self.logger.info(f"思考模型分析器初始化")
        self.logger.info(f"思考超时: {self.thinking_timeout} 秒")
        self.logger.info(f"等待完整思考: {self.wait_for_complete_thinking}")
    
    async def analyze_with_thinking_model(self, news: Dict[str, Any], system_prompt: str) -> Optional[Dict[str, Any]]:
        """使用思考模型分析新闻"""
        api_key = self.config.get('api_key', '')
        api_url = self.config.get('api_url', '')
        proxy_url = self.config.get('proxy_url', '')
        
        if not api_key:
            raise Exception("API密钥未配置")
        
        # 构建分析提示
        user_prompt = self._build_analysis_prompt(news)
        
        headers = {
            'Content-Type': 'application/json',
            'x-goog-api-key': api_key
        }
        
        # 为思考模型优化的配置
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": f"{system_prompt}\n\n{user_prompt}"
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": self.config.get('temperature', 0.7),
                "maxOutputTokens": self.config.get('max_tokens', 8192),
                "topP": self.config.get('topP', 0.95),
                "topK": self.config.get('topK', 40)
            }
        }
        
        start_time = time.time()
        self.logger.info(f"开始思考模型分析: {news.get('title', '未知标题')[:50]}...")
        
        try:
            connector = aiohttp.TCPConnector()
            timeout = aiohttp.ClientTimeout(total=self.thinking_timeout)
            
            async with aiohttp.ClientSession(
                connector=connector, 
                timeout=timeout
            ) as session:
                
                self.logger.info(f"发送请求到思考模型，最大等待时间: {self.thinking_timeout} 秒")
                
                async with session.post(
                    api_url, 
                    headers=headers, 
                    json=payload,
                    proxy=proxy_url if proxy_url else None
                ) as response:
                    
                    elapsed_time = time.time() - start_time
                    self.logger.info(f"思考模型响应状态: {response.status} (思考时间: {elapsed_time:.1f}秒)")
                    
                    if response.status == 200:
                        result = await response.json()
                        return await self._process_thinking_response(result, elapsed_time, news)
                    else:
                        error_text = await response.text()
                        self.logger.error(f"思考模型API调用失败: {response.status} - {error_text}")
                        return None
                        
        except asyncio.TimeoutError:
            elapsed_time = time.time() - start_time
            self.logger.warning(f"思考模型超时 ({elapsed_time:.1f}秒)，模型可能还在深度思考中")
            return None
        except Exception as e:
            elapsed_time = time.time() - start_time
            self.logger.error(f"思考模型分析异常 ({elapsed_time:.1f}秒): {e}")
            return None
    
    async def _process_thinking_response(self, result: Dict, elapsed_time: float, news: Dict) -> Optional[Dict[str, Any]]:
        """处理思考模型的响应"""
        if 'candidates' not in result or len(result['candidates']) == 0:
            self.logger.error("思考模型响应中没有候选项")
            return None
        
        candidate = result['candidates'][0]
        finish_reason = candidate.get('finishReason', 'UNKNOWN')
        
        self.logger.info(f"思考模型完成原因: {finish_reason}")
        
        # 分析使用情况
        if 'usageMetadata' in result:
            usage = result['usageMetadata']
            prompt_tokens = usage.get('promptTokenCount', 0)
            total_tokens = usage.get('totalTokenCount', 0)
            thoughts_tokens = usage.get('thoughtsTokenCount', 0)
            output_tokens = total_tokens - prompt_tokens - thoughts_tokens
            
            self.logger.info(f"Token使用情况:")
            self.logger.info(f"  输入tokens: {prompt_tokens}")
            self.logger.info(f"  思考tokens: {thoughts_tokens}")
            self.logger.info(f"  输出tokens: {output_tokens}")
            self.logger.info(f"  总tokens: {total_tokens}")
            
            # 检查是否真的在思考
            if thoughts_tokens > 0:
                self.logger.info(f"✅ 模型进行了深度思考 ({thoughts_tokens} tokens)")
            else:
                self.logger.info(f"ℹ️ 模型没有进入思考模式")
        
        # 提取文本内容
        content_text = None
        
        if 'content' in candidate and 'parts' in candidate['content']:
            parts = candidate['content']['parts']
            if len(parts) > 0 and 'text' in parts[0]:
                content_text = parts[0]['text'].strip()
                self.logger.info(f"✅ 成功提取思考结果: {len(content_text)} 字符")
        
        if content_text:
            return {
                "markdown_report": content_text,
                "sentiment": self._extract_sentiment(content_text),
                "entities": self._extract_entities(content_text),
                "summary": self._extract_summary(content_text),
                "confidence": 0.95,  # 思考模型置信度更高
                "model_used": "gemini-2.5-pro-thinking",
                "thinking_time": elapsed_time,
                "thinking_tokens": result.get('usageMetadata', {}).get('thoughtsTokenCount', 0)
            }
        else:
            # 特殊处理思考模型的情况
            if finish_reason == 'MAX_TOKENS':
                self.logger.warning(f"思考模型达到最大tokens限制，可能需要增加max_tokens配置")
            elif finish_reason == 'STOP':
                self.logger.warning(f"思考模型正常停止但没有输出文本，可能是提示词问题")
            
            self.logger.error(f"思考模型响应中未找到文本内容")
            self.logger.debug(f"完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return None
    
    def _build_analysis_prompt(self, news: Dict[str, Any]) -> str:
        """构建分析提示"""
        title = news.get('title', '')
        content = news.get('content', '')
        source = news.get('source', '')
        timestamp = news.get('timestamp', '')
        
        prompt = f"""
[财联社新闻原文]
标题: {title}
来源: {source}
时间: {timestamp}
内容: {content}

请按照系统提示中的格式要求，对这条新闻进行深度的A股量化投研分析。

请充分发挥您的思考能力，进行深入的分析，包括：
1. 深度的事件解析和影响评估
2. 全面的产业链分析和传导效应
3. 精确的量化预测和风险评估
4. 专业的投资建议和观察要点

请提供高质量、专业的分析报告。
"""
        
        return prompt.strip()
    
    def _extract_sentiment(self, text: str) -> str:
        """提取情感倾向"""
        if '正面' in text or '利好' in text or '增长' in text:
            return '正面'
        elif '负面' in text or '利空' in text or '下降' in text:
            return '负面'
        else:
            return '中性'
    
    def _extract_entities(self, text: str) -> List[str]:
        """提取核心实体"""
        entities = []
        
        # 简单的实体提取
        import re
        
        # 提取股票代码
        stock_codes = re.findall(r'\d{6}', text)
        entities.extend(stock_codes)
        
        # 提取公司名称
        company_names = re.findall(r'[\u4e00-\u9fff]+(?:股份|证券|集团|科技|医药|能源)', text)
        entities.extend(company_names[:3])  # 最多3个
        
        return list(set(entities))[:5]  # 去重，最多5个
    
    def _extract_summary(self, text: str) -> str:
        """提取摘要"""
        lines = text.split('\n')
        
        # 查找事件摘要部分
        for i, line in enumerate(lines):
            if '事件摘要' in line and i + 1 < len(lines):
                summary_line = lines[i + 1].strip()
                if summary_line.startswith('-') or summary_line.startswith('*'):
                    summary_line = summary_line[1:].strip()
                return summary_line
        
        # 如果没找到，返回前100个字符
        clean_text = text.replace('#', '').replace('*', '').replace('-', '').strip()
        return clean_text[:100] + "..." if len(clean_text) > 100 else clean_text
